```sql
with
t1 as (select cr_dlv_h_id,cr_dlv_h_no,client_no,cr_dlv_type from cr_dlv_h where cr_dlv_h_rmk6='发货完成' and coalesce(sap_bill_no,'')=''),
t2 as (select * from cr_dlv_b where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t3 as (select * from cr_dlv_sn_part where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t4 as (select cr_dlv_sn_part_rmk6,lot_no,invp_no,sum(part_qty) as part_qty from t3 group by cr_dlv_sn_part_rmk6,lot_no,invp_no),
t5 as (select t2.cr_dlv_h_id,t2.cr_dlv_b_id,t2.part_no,t2.price,t2.so_h_no,t2.so_b_id,coalesce(t2.cr_dlv_b_rmk5,'0') as ctn_num,
	t4.lot_no,t4.invp_no,t4.part_qty from t2 left join t4 on t4.cr_dlv_sn_part_rmk6=t2.cr_dlv_b_id ),
t6 as (select t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,sum(t5.part_qty) as part_qty_t,t5.ctn_num,json_agg(json_build_object('BatchNo',lot_no,'Qty',t5.part_qty)) as batch_no
	from t1 
	left join t5 on t5.cr_dlv_h_id=t1.cr_dlv_h_id
	group by t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,t5.ctn_num),
t7 as (select cr_dlv_h_no,cr_dlv_type,
	case when cr_dlv_type='订单' then 
	json_agg(json_build_object('BaseType','17','BaseRef',so_h_no,'BaseLine',split_part(so_b_id,'_',2),'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,
	'U_PLqty',ctn_num,'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2),'U_JCDOC',so_h_no,'U_JCLINE',split_part(so_b_id,'_',2)))
	else
	json_agg(json_build_object('ItemCode',part_no,'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,'U_PLqty',ctn_num,'U_basedoc',so_h_no,
	'U_baseline',split_part(so_b_id,'_',2),'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2)))
	end as datas
	from t6
	group by cr_dlv_h_no,cr_dlv_type)

select t7.cr_dlv_h_no,json_build_object('CardCode',t1.client_no,'DocDate',to_char(current_date,'yyyy-mm-dd'),'OwnerCode','379','U_WebNo',t1.cr_dlv_h_no,'details',t7.datas) as datas
from t1 
left join t7 on t7.cr_dlv_h_no=t1.cr_dlv_h_no

```

```javascript
function main() {
	var success=[];
	var fail=[];

var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	//Log.LogInfo(req_params.me_finish_io_no);
	var cr_dlv_no=req_params.cr_dlv_h_no;

	var params={
		"method": "mes-sap-ODLN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));	
  var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		ReadContext.ExecuteSql("update cr_dlv_h set sap_bill_no='"+result.message+"',upd_time=localtimestamp where cr_dlv_h_no='"+cr_dlv_no+"'");
	}

}

return {"success":success,"fail":fail};
 }
```

逻辑分析：

## 一、数据逻辑分析

### 1. 数据流转路径
```
cr_dlv_h（发货单头） --> cr_dlv_b（发货单体） --> cr_dlv_sn_part（拣货记录）
                                                          ↓
                                              按批次/库位汇总 → SAP接口JSON
```

### 2. 核心数据处理
- **t1**: 筛选"发货完成"且未传SAP的出库单
- **t2**: 获取对应的出库单明细
- **t3**: 获取对应的拣货记录
- **t4**: 按明细行ID(cr_dlv_sn_part_rmk6)、批次、库位汇总数量
- **t5**: 关联明细和拣货汇总，获取箱数(cr_dlv_b_rmk5)
- **t6**: 再次汇总，生成批次JSON数组
- **t7**: 根据出库类型构建SAP接口所需的JSON格式

## 二、业务逻辑分析

### 1. 业务场景
- 支持两种出库类型：
  - **订单类型**：基于销售订单的出库，需要关联原始销售订单信息
  - **非订单类型**：直接出库，不关联销售订单

### 2. 关键业务字段
- `cr_dlv_h_rmk6='发货完成'`：标识可以推送SAP的单据
- `sap_bill_no`：SAP返回的单据号，用于防止重复推送
- `cr_dlv_b_rmk5`：箱数信息，在SAP中对应`U_PLqty`字段
- `cr_dlv_sn_part_rmk6`：存储对应的明细行ID，用于关联拣货记录和明细

### 3. 批次管理
- 系统支持批次管理，将同一明细行的不同批次数量打包成JSON数组
- 批次信息包含：批次号(BatchNo)和数量(Qty)

## 三、代码逻辑分析

### 1. SQL查询逻辑
```sql
-- 关键关联逻辑
t4: group by cr_dlv_sn_part_rmk6,lot_no,invp_no  -- 按明细行、批次、库位汇总
t5: left join t4 on t4.cr_dlv_sn_part_rmk6=t2.cr_dlv_b_id  -- 通过rmk6字段关联
```

### 2. JavaScript处理逻辑
- 循环处理每个待推送的出库单
- 调用SAP接口推送数据
- 成功后更新`sap_bill_no`，标记已推送
- 失败时记录错误信息并抛出异常

### 3. 潜在问题
- **箱数问题**：`cr_dlv_b_rmk5`字段依赖于拣货完成时的更新，如果更新逻辑有误，会导致箱数不准确
- **事务处理**：JavaScript代码中直接执行SQL更新，缺少事务控制

## 四、表关联逻辑分析

### 1. 主要表关系
```mermaid
graph TD
    A[cr_dlv_h] -->|cr_dlv_h_id| B[cr_dlv_b]
    A -->|cr_dlv_h_id| C[cr_dlv_sn_part]
    B -->|cr_dlv_b_id = cr_dlv_sn_part_rmk6| C
    
    style A fill:#ff9999,stroke:#333,stroke-width:2px
    style B fill:#99ccff,stroke:#333,stroke-width:2px
    style C fill:#99ff99,stroke:#333,stroke-width:2px
```

### 2. 关键关联字段
- `cr_dlv_h_id`：出库单主键，连接单头和明细
- `cr_dlv_b_id`：明细主键，通过`cr_dlv_sn_part_rmk6`与拣货记录关联
- `cr_dlv_sn_part_rmk6`：存储明细ID，实现拣货记录到明细的反向关联

### 3. 数据完整性依赖
- 依赖`cr_dlv_h_rmk6`状态准确性
- 依赖`cr_dlv_sn_part_rmk6`正确存储明细ID
- 依赖`cr_dlv_b_rmk5`箱数信息的准确更新

## 五、优化建议

1. **增加数据校验**：推送前校验关键字段完整性
2. **事务控制**：JavaScript中的更新操作应该包含在事务中
3. **错误处理**：增加更详细的错误日志和重试机制
4. **性能优化**：对于大批量数据，考虑分批处理
