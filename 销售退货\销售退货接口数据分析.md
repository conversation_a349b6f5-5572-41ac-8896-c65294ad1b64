# 销售退货接口数据分析文档

## 概述
本文档分析了销售退货系统的接口数据结构，包括SAP接口请求数据格式、PDA扫描处理流程以及相关数据表结构。

## 1. SAP接口请求数据结构

### 1.1 接口数据查询（接口数据.sql）
该查询用于生成向SAP系统发送的销售退货数据，格式为JSON结构。

#### 主要字段说明：
- **CardCode**: 客户编号 (`sa.client_no`)
- **DocDate**: 单据日期 (`sa.upd_time` 格式化为 yyyy-mm-dd)
- **OwnerCode**: 固定值 '379'
- **U_WebNo**: 退货单号 (`sa.cr_rtn_h_no`)
- **details**: 明细数组，包含以下字段：
  - `ItemCode`: 物料编号
  - `QuanTity`: 实际数量
  - `BatchNo`: 批次号
  - `WhsCode`: 仓库区域
  - `PriceAfterVAT`: 含税价格
  - `Currency`: 币种
  - `EnSetCost`: 成本价格
  - `RetCost`: 库存价格
  - `U_BaseType`: 固定值 '销售交货'
  - `U_QtDocNum`: 销售订单号
  - `U_QtLine`: 销售订单行号（从so_b_id中提取）
  - `U_KHPO`: 客户PO号

#### 触发条件：
- 仓库扫描收货完成 (`sa.cr_rtn_rmk06 = '仓库扫描收货完成'`)

## 2. PDA扫描处理流程

### 2.1 主函数：af_pda_wms_sales_rtn_inbound
销售退货扫描的入口函数，负责：
- 验证退货单存在性
- 检查收货状态
- 验证产品出库状态（sn_status='830'）
- 根据need_new_sn标志调用相应处理函数

#### 输入参数：
```json
{
  "user_no": "用户编号",
  "datas": [{
    "bill_no": "退货单号",
    "sn_no": "产品序列号"
  }]
}
```

#### 业务验证：
1. 退货单必须存在
2. 退货单未完成收货
3. 产品必须是出库状态（830）

### 2.2 新序列号处理：af_pda_wms_sales_rtn_new_sn
适用于需要生成新序列号的退货场景：

#### 处理逻辑：
1. 验证序列号属于当前退货单
2. 检查是否已收货完成
3. 更新退货明细表状态
4. 更新序列号部件表状态
5. 更新序列号状态为在库（800）
6. 检查整单是否完成

### 2.3 原序列号处理：af_pda_wms_sales_rtn_orig_sn
适用于使用原序列号的退货场景：

#### 处理逻辑：
1. 防止重复扫描
2. 验证物料编号匹配
3. 检查数量限制
4. 插入序列号部件记录
5. 更新序列号状态和库位信息
6. 处理包装序列号（sn_type='40'）
7. 检查明细和整单完成状态

## 3. 数据表结构

### 3.1 退货单头表（szjy_mes_cr_rtn_h）
| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| cr_rtn_h_id | text | 退货单头ID | af_auid() |
| cr_rtn_h_no | text | 退货单号 | 自动生成 |
| cr_rtn_datetime | date | 退货日期 | 当前日期 |
| client_no | text | 客户编号 | - |
| client_name | text | 客户名称 | - |
| so_h_no | text | 销售订单号 | - |
| currency | text | 币种 | - |
| need_new_sn | bool | 是否需要新序列号 | true |
| cr_rtn_rmk06 | text | 状态标记 | - |

### 3.2 退货单明细表（szjy_mes_cr_rtn_b）
| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| cr_rtn_b_id | text | 明细ID | af_auid() |
| cr_rtn_h_id | text | 退货单头ID | - |
| part_no | text | 物料编号 | - |
| part_qty_plan | numeric | 计划数量 | 0 |
| part_qty_real | numeric | 实际数量 | 0 |
| lot_no | text | 批次号 | - |
| invp_area | text | 库位 | - |
| so_h_no | text | 销售订单号 | - |
| so_b_id | text | 销售订单明细ID | - |
| price | numeric | 价格 | 0 |
| price_afvat | numeric | 含税价格 | 0 |
| stock_price | numeric | 库存价格 | 0 |
| u_khpo | text | 客户PO号 | - |

## 4. 业务流程图

```mermaid
graph TD
    A[PDA扫描退货单] --> B{退货单存在?}
    B -->|否| C[返回错误]
    B -->|是| D{已收货完成?}
    D -->|是| E[返回错误]
    D -->|否| F{产品出库状态?}
    F -->|否| G[返回错误]
    F -->|是| H{需要新序列号?}
    H -->|是| I[新序列号处理]
    H -->|否| J[原序列号处理]
    I --> K[更新状态]
    J --> L[插入记录]
    K --> M{整单完成?}
    L --> M
    M -->|是| N[生成SAP接口数据]
    M -->|否| O[等待继续扫描]
```

## 5. 关键状态说明

### 5.1 序列号状态
- **800**: 在库状态
- **830**: 出库状态

### 5.2 收货状态标记
- **cr_rtn_rmk06**: '仓库扫描收货完成' - 表示该项目已完成收货

## 6. 注意事项

1. **数量控制**: 系统严格控制退货数量不能超过计划数量
2. **状态验证**: 只有出库状态的产品才能进行退货
3. **重复扫描**: 系统防止同一产品重复扫描
4. **包装处理**: 对于包装类型产品（sn_type='40'）需要特殊处理
5. **事务完整性**: 所有操作都在事务中进行，确保数据一致性

## 7. 错误处理

系统包含完善的错误处理机制：
- 业务逻辑验证错误
- 数据库操作异常
- 详细的错误信息返回
- 操作日志记录

## 8. 接口集成

当退货单完全收货完成后，系统会自动生成符合SAP接口规范的JSON数据，用于与SAP系统进行数据同步。
