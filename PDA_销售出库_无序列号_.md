```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_no_sn(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_no_sn(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
*  功能：无序列号管控产品销售出库
*  描述：
*  时间：
*  开发者：
*/
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;
		_part_qty_plan numeric;
		_part_qty_stock numeric;

		row_datas record;
		stock_datas record;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;

	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			res := row('false', '扫描销售发货单不存在 或者 销售发货单已经发货完成.');
			return to_json(res);
		end if;
		select cr_dlv_h_id into _bill_id from cr_dlv_h where cr_dlv_h_no=_bill_no;
		for row_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id) loop
			if not exists(select 1 from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no and part_qty>0) then
				res := row('false', format('销售出库产品 %s 发货仓库 %s 在仓库库存中不存在，不能出库', row_datas.part_no, row_datas.invp_no));
				return to_json(res);
			end if;
			
			select sum(part_qty) into _part_qty_stock from szjy_wm_inventory where part_no=row_datas.part_no;
			if row_datas.cr_dlv_qty_plan>_part_qty_stock then
				res := row('false', format('销售出库产品 %s 数量 %s 大于库存数量 %s，不能出库', row_datas.part_no, row_datas.part_qty_plan, _part_qty_stock));
				return to_json(res);
			end if;
		end loop;

		for row_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id) loop
			_part_qty_plan := row_datas.cr_dlv_qty_plan;
			for stock_datas in (select * from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no order by lot_no) loop
				if _part_qty_plan > 0 then
					if _part_qty_plan <= stock_datas.part_qty then
					
						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						values(af_auid(), row_datas.cr_dlv_h_id, '', row_datas.part_no, row_datas.part_name, row_datas.part_spec, row_datas.part_unit, '', '', '', stock_datas.lot_no, row_datas.invp_no, '', _part_qty_plan, '', '', '', '', '', row_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

						update public.szjy_wm_inventory set part_qty=part_qty-_part_qty_plan,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
						where inventory_id=	stock_datas.inventory_id;					

						_part_qty_plan := 0;
					else
						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						values(af_auid(), row_datas.cr_dlv_h_id, '', row_datas.part_no, row_datas.part_name, row_datas.part_spec, row_datas.part_unit, '', '', '', stock_datas.lot_no, row_datas.invp_no, '', stock_datas.part_qty, '', '', '', '', '', row_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');
			
						update public.szjy_wm_inventory set part_qty=0,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
						where inventory_id=	stock_datas.inventory_id;

						_part_qty_plan := _part_qty_plan-stock_datas.part_qty;
					end if;
				end if;
			end loop;
		end loop;

		update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty_plan,cr_dlv_b_rmk6='发货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id;

		update public.cr_dlv_h set cr_dlv_h_rmk6='发货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id;

		res := row('true', '---OK---');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;


```

## 功能逻辑分析

### 一、数据逻辑分析

#### 1. 数据流转路径
```
扫描出库单号 → 验证库存 → 按批次FIFO分配 → 生成拣货记录 → 扣减库存
                                    ↓
                            更新出库单状态为"发货完成"
```

#### 2. 核心数据处理流程
1. **单据验证**：验证出库单存在且未发货（`cr_dlv_h_rmk6=''`）
2. **库存验证**：验证每个产品在指定仓库有足够库存
3. **批次分配**：按批次号（lot_no）排序，先进先出分配库存
4. **拣货记录**：在`cr_dlv_sn_part`表创建记录（虽然无序列号，但记录批次信息）
5. **库存扣减**：更新`szjy_wm_inventory`表的库存数量
6. **状态更新**：更新出库单为"发货完成"状态

### 二、业务逻辑分析

#### 1. 业务场景
- **无序列号管控产品的销售出库**
- 适用于批次管理但不需要序列号追踪的产品
- 一次性完成整单出库，无需逐个扫描

#### 2. 关键业务规则

##### 2.1 库存验证规则
```sql
-- 验证指定仓库有库存
if not exists(select 1 from szjy_wm_inventory 
    where part_no=row_datas.part_no 
    and invp_area=row_datas.invp_no 
    and part_qty>0) then
    -- 报错：库存不存在
end if;

-- 验证总库存足够
select sum(part_qty) into _part_qty_stock 
from szjy_wm_inventory 
where part_no=row_datas.part_no;

if row_datas.cr_dlv_qty_plan > _part_qty_stock then
    -- 报错：库存不足
end if;
```

##### 2.2 批次分配逻辑（FIFO）
```sql
-- 按批次号排序，优先使用早期批次
for stock_datas in (
    select * from szjy_wm_inventory 
    where part_no=row_datas.part_no 
    and invp_area=row_datas.invp_no 
    order by lot_no  -- 批次号排序实现FIFO
) loop
    -- 分配库存直到满足需求
end loop
```

##### 2.3 库存扣减策略
- 如果当前批次库存足够，全部从该批次扣减
- 如果当前批次库存不足，扣完后继续下一批次
- 记录每个批次的实际出库数量

#### 3. 与有序列号出库的区别
- **无需扫描**：一次性处理整个出库单
- **批次管理**：按批次而非序列号管理
- **简化流程**：跳过拣货和品质扫描环节，直接完成

### 三、代码逻辑分析

#### 1. 两层循环处理
```sql
-- 外层循环：遍历出库单明细
for row_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id) loop
    _part_qty_plan := row_datas.cr_dlv_qty_plan;
    
    -- 内层循环：遍历库存批次
    for stock_datas in (select * from szjy_wm_inventory ...) loop
        if _part_qty_plan > 0 then
            -- 分配库存
        end if;
    end loop;
end loop
```

#### 2. 库存分配算法
```sql
if _part_qty_plan <= stock_datas.part_qty then
    -- 情况1：当前批次足够
    -- 创建拣货记录（数量=需求量）
    -- 扣减库存（扣减量=需求量）
    _part_qty_plan := 0;  -- 需求已满足
else
    -- 情况2：当前批次不足
    -- 创建拣货记录（数量=批次库存）
    -- 清空该批次库存
    _part_qty_plan := _part_qty_plan - stock_datas.part_qty;  -- 剩余需求
end if
```

#### 3. 状态更新
- 明细状态：`cr_dlv_b_rmk6='发货完成'`
- 单头状态：`cr_dlv_h_rmk6='发货完成'`
- 实际数量：`cr_dlv_qty=cr_dlv_qty_plan`

### 四、表关联逻辑分析

#### 1. 核心表关系
```mermaid
graph TD
    A[cr_dlv_h 出库单头] -->|cr_dlv_h_id| B[cr_dlv_b 出库单明细]
    C[szjy_wm_inventory 批次库存] -->|批次分配| D[cr_dlv_sn_part 出库记录]
    B -->|生成| D
    E[ss_user 用户] -->|操作人| A
    E -->|操作人| D
    
    C -->|扣减| F[库存更新]
    
    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#ffcc99
    style D fill:#99ff99
```

#### 2. 关键字段说明
- `cr_dlv_sn_part`表虽然名称含"sn"，但在无序列号场景下：
  - `sn_no`字段为空
  - `lot_no`字段记录批次号
  - `part_qty`记录该批次的出库数量
  - `cr_dlv_sn_part_rmk6`记录对应的明细ID

#### 3. 数据完整性保证
- 事务控制：整个函数在一个事务中执行
- 库存验证：出库前验证库存充足性
- 状态同步：库存扣减与出库记录同步

### 五、潜在问题与优化建议

#### 1. 性能问题
**现状**：双重循环可能导致性能问题
**建议**：
```sql
-- 使用窗口函数优化批次分配
WITH batch_allocation AS (
    SELECT *,
        SUM(part_qty) OVER (PARTITION BY part_no ORDER BY lot_no) as cumulative_qty
    FROM szjy_wm_inventory
    WHERE part_no = ? AND invp_area = ?
)
-- 直接计算每个批次的分配量
```

#### 2. 并发控制
**问题**：未见显式锁机制，可能存在并发扣减问题
**建议**：
```sql
-- 在查询库存时加锁
SELECT * FROM szjy_wm_inventory 
WHERE ... 
FOR UPDATE;
```

#### 3. 错误处理增强
- 增加批次分配失败的详细日志
- 记录每个批次的分配明细，便于追溯

#### 4. 功能增强
- 支持部分发货（当前是全部或失败）
- 支持指定批次出库（当前强制FIFO）
- 增加库存预占功能，避免并发冲突

### 六、与整体流程的关系

在执行流程中，无序列号出库是有序列号出库的简化版本：
- **跳过环节**：拣货、品质扫描
- **直接完成**：扫描单号后直接完成出库
- **适用场景**：大批量、低价值、无需精确追踪的产品

这种设计体现了系统的灵活性，能够根据不同产品特性采用不同的出库策略。


我来对 `PDA_销售出库_无序列号_.md` 进行详细的逻辑分析。
我已经在 `PDA_销售出库_无序列号_.md` 文件末尾添加了详细的功能逻辑分析。

## 关键发现总结：

### 1. **业务定位**
- 这是针对无序列号管控产品的快速出库方案
- 适用于批次管理的产品，但不需要精确到每个序列号
- 一次性完成整单出库，大大简化了操作流程

### 2. **与有序列号出库的对比**
| 特性 | 有序列号出库 | 无序列号出库 |
|------|------------|------------|
| 扫描方式 | 逐个扫描 | 扫描单号即可 |
| 流程环节 | 拣货→品质→发货 | 直接发货完成 |
| 库存管理 | 序列号级别 | 批次级别 |
| 适用场景 | 高价值、需追溯 | 低价值、大批量 |

### 3. **技术特点**
- 使用双重循环实现批次FIFO分配
- 复用`cr_dlv_sn_part`表存储批次出库记录
- 直接更新状态为"发货完成"，跳过中间环节

### 4. **潜在风险**
- **并发问题**：缺少显式的库存锁定机制
- **性能问题**：双重循环在大数据量时可能较慢
- **灵活性不足**：强制FIFO，不支持指定批次

### 5. **在整体流程中的位置**
根据执行流程图，无序列号出库是一个独立的分支，直接从"销售出库_SAP申请_列表"到"接口_MES-SAP销售发货单"，体现了系统设计的灵活性。

