# 销售退货入库PDA功能分析报告

## 1. 概述

本报告分析了销售退货入库PDA系统的核心功能实现，该系统通过三个PostgreSQL存储过程实现了完整的销售退货扫描入库流程。

## 2. 系统架构

### 2.1 函数结构
```
af_pda_wms_sales_rtn_inbound (主控制器)
├── af_pda_wms_sales_rtn_new_sn (新序列号处理)
└── af_pda_wms_sales_rtn_orig_sn (原序列号处理)
```

### 2.2 数据流向
```
PDA扫描输入 → 主控制器验证 → 分支处理 → 状态更新 → 返回结果
```

## 3. 功能模块详细分析

### 3.1 主控制器函数：af_pda_wms_sales_rtn_inbound

#### 功能职责
- 接收PDA扫描数据
- 执行业务规则验证
- 路由到具体处理函数
- 统一异常处理

#### 输入参数结构
```json
{
  "user_no": "操作用户编号",
  "datas": [{
    "bill_no": "退货单号",
    "sn_no": "产品序列号"
  }]
}
```

#### 核心验证逻辑
1. **退货单存在性验证**
   ```sql
   if not exists(select 1 from szjy_mes_cr_rtn_h where cr_rtn_h_no=_bill_no)
   ```

2. **收货状态验证**
   ```sql
   if exists(select 1 from szjy_mes_cr_rtn_h where cr_rtn_h_no=_bill_no 
             and coalesce(cr_rtn_rmk06,'')='仓库扫描收货完成')
   ```

3. **产品状态验证**
   ```sql
   if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='830')
   ```

#### 路由决策
基于`need_new_sn`标志决定处理方式：
- `true`: 调用新序列号处理函数
- `false`: 调用原序列号处理函数

### 3.2 新序列号处理：af_pda_wms_sales_rtn_new_sn

#### 适用场景
适用于需要重新生成序列号的退货场景，通常用于：
- 产品重新包装
- 序列号重新分配
- 特殊退货处理

#### 核心业务逻辑

1. **序列号归属验证**
   ```sql
   if not exists(select 1 from szjy_mes_cr_rtn_sn_part 
                 where cr_rtn_h_id=_bill_id and sn_no=_sn_no)
   ```

2. **重复扫描检查**
   ```sql
   if exists(select 1 from szjy_mes_cr_rtn_sn_part 
             where cr_rtn_h_id=_bill_id and sn_no=_sn_no 
             and coalesce(cr_rtn_rmk06,'')='仓库扫描收货完成')
   ```

3. **数据更新操作**
   - 更新退货明细表实际数量
   - 标记序列号部件收货完成
   - 更新序列号状态为在库(800)
   - 检查整单完成状态

#### 关键更新语句
```sql
-- 更新明细表
update szjy_mes_cr_rtn_b set 
  part_qty_real=part_qty_plan,
  cr_rtn_rmk06='仓库扫描收货完成'
where cr_rtn_b_id=_rtn_b_id;

-- 更新序列号状态
update wm_sn set 
  sn_status='800',
  sn_status_name='在库'
where sn_no=_sn_no;
```

### 3.3 原序列号处理：af_pda_wms_sales_rtn_orig_sn

#### 适用场景
适用于保持原序列号的退货场景，是最常见的处理方式。

#### 核心业务逻辑

1. **重复扫描防护**
   ```sql
   if exists(select 1 from szjy_mes_cr_rtn_sn_part 
             where cr_rtn_h_id=_bill_id and sn_no=_sn_no)
   ```

2. **物料匹配验证**
   ```sql
   if not exists(select distinct 1 from szjy_mes_cr_rtn_b 
                 where cr_rtn_h_id=_bill_id and part_no=_part_no)
   ```

3. **数量控制检查**
   ```sql
   if _part_qty_real+_part_qty > _part_qty_plan then
     -- 超量检查逻辑
   end if;
   ```

4. **序列号部件记录创建**
   ```sql
   insert into szjy_mes_cr_rtn_sn_part
   (cr_rtn_sn_id, cr_rtn_h_id, sn_no, part_no, ...)
   select af_auid(), _bill_id, _sn_no, _part_no, ...
   ```

#### 特殊处理：包装序列号
```sql
if _sn_type='40' then
  update wm_sn set 
    sn_status='800',
    sn_status_name='在库',
    inventory_lot=_lot_no,
    invp_area_no=_invp_area
  where sn_pack_50=_sn_no;
end if;
```

## 4. 数据状态管理

### 4.1 序列号状态转换
```
出库状态(830) → 扫描验证 → 在库状态(800)
```

### 4.2 收货完成标记
- **cr_rtn_rmk06**: '仓库扫描收货完成'
- 用于标记明细行和整单的完成状态

### 4.3 完成状态检查逻辑
```sql
if not exists(select distinct 1 from szjy_mes_cr_rtn_b 
              where cr_rtn_h_id=_bill_id 
              and coalesce(cr_rtn_rmk06,'') != '仓库扫描收货完成') then
  -- 标记整单完成
  update szjy_mes_cr_rtn_h set cr_rtn_rmk06='仓库扫描收货完成'
end if;
```

## 5. 错误处理机制

### 5.1 业务异常处理
系统定义了详细的业务错误信息：
- "扫描销售退货单不存在"
- "扫描销售退货单已经收货完成，不能收货"
- "扫描产品不是出库状态，不能退货"
- "扫描产品已经退货扫描，不能二次扫描"
- "扫描产品编码不属于销售退货单内，不能销售退货"

### 5.2 系统异常处理
```sql
exception when others then
  GET STACKED diagnostics
    _err_msg_text = MESSAGE_TEXT,
    _err_pg_detail = PG_EXCEPTION_DETAIL;
  
  _err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
  res := row('false',_err_msg);
  return to_json(res);
```

## 6. 性能考虑

### 6.1 索引建议
基于查询模式，建议创建以下索引：
```sql
-- 退货单号索引
CREATE INDEX idx_cr_rtn_h_no ON szjy_mes_cr_rtn_h(cr_rtn_h_no);

-- 序列号索引
CREATE INDEX idx_wm_sn_no ON wm_sn(sn_no);

-- 退货明细复合索引
CREATE INDEX idx_cr_rtn_b_h_part ON szjy_mes_cr_rtn_b(cr_rtn_h_id, part_no);
```

### 6.2 查询优化
- 使用EXISTS代替COUNT进行存在性检查
- 合理使用COALESCE处理NULL值
- 批量更新操作减少数据库交互

## 7. 安全性分析

### 7.1 数据完整性
- 事务处理确保数据一致性
- 外键约束保证数据关联完整性
- 业务规则验证防止非法操作

### 7.2 并发控制
- 使用数据库事务隔离级别
- 乐观锁机制防止并发冲突
- 状态检查防止重复操作

## 8. 日志记录

### 8.1 操作日志
```sql
insert into log_test(value,type) 
values(_bill_no||'/'||_sn_no,'af_pda_wms_sales_rtn_inbound>销售退货入库');

insert into a_test_log 
values(datas, 'new_sn', localtimestamp);
```

### 8.2 审计字段
每个表都包含完整的审计字段：
- crt_time, crt_user, crt_user_no, crt_user_name
- upd_time, upd_user, upd_user_no, upd_user_name

## 9. 改进建议

### 9.1 代码优化
1. **参数验证增强**: 增加输入参数的格式验证
2. **错误码标准化**: 使用标准错误码替代文本消息
3. **配置外部化**: 将硬编码值移至配置表

### 9.2 功能增强
1. **批量处理**: 支持一次扫描多个序列号
2. **状态回滚**: 提供退货撤销功能
3. **实时通知**: 完成时发送通知消息

### 9.3 监控告警
1. **性能监控**: 监控函数执行时间
2. **异常告警**: 异常情况实时告警
3. **业务指标**: 退货完成率等业务指标监控

## 10. 总结

该PDA销售退货入库系统设计完善，具有以下特点：
- **业务逻辑清晰**: 分层设计，职责明确
- **错误处理完善**: 覆盖各种异常情况
- **数据一致性强**: 事务处理确保数据完整
- **扩展性良好**: 支持不同的退货处理模式
- **可维护性高**: 代码结构清晰，注释完整

系统能够有效支持企业的销售退货业务需求，为仓库管理提供了可靠的技术支撑。
