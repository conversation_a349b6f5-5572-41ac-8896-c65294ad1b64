## 通知单校验：af_sales_notice_head

```sql
-- DROP FUNCTION public.af_sales_notice_head(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_sales_notice_head(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**  

/*

---扫描前校验   select af_sales_notice_head('k') 

*/		 



**declare**

​    _datas **json**;

​     _results returntype; 

​    _sn_no **varchar**; 

​    _tzdh **varchar**;

​    js1 **json**;

​    js2 **json**;

​    js3 **json**; 

​    _通知单号  **varchar**;

​    _cr_dlv_h_no **text**;

 	tmp_json **json**[];

**begin**  

​	



  	_datas := **json**(datas);

  	_datas := _datas ->> 'datas';

​    _datas := _datas ->> 0;

​    _cr_dlv_h_no := _datas ->> 'cr_dlv_h_no';

   

​	

​	**if** **not** **exists**(**select** 1 **from** cr_dlv_h h **where** h.cr_dlv_h_no = _cr_dlv_h_no) **then**

​		_results:=**row**('false',**concat**('扫描的出货申请单:',_cr_dlv_h_no,'不存在!!!'));

​		**return**  **to_json**(_results);  

​	**end** **if**;



   **SELECT** **ARRAY_AGG**(**ROW_TO_JSON**(a))

​	**INTO** tmp_json

​	**FROM** (**SELECT**  h.cr_dlv_h_no 

​		  **FROM** cr_dlv_h h   

​		  **WHERE** h.cr_dlv_h_no = _cr_dlv_h_no

​		 ) a;

​	

   **return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);

​	**END**;

**$function$**

;


```



## 条码校验：af_sales_notice_bf



```sql
-- DROP FUNCTION public.af_sales_notice_bf(varchar);

**CREATE** **OR** **REPLACE** **FUNCTION** public.af_sales_notice_bf(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**  

/*

---扫描前校验   select af_sales_notice_bf('k') 

功能：校验条码

描述：

时间：

开发者：LLY



*/		 

\--

**declare**

​    _datas **json**;

​     res returntype; 

​    _sn_no **varchar**;  

​    _cr_dlv_h_no **text**;

   	_cr_dlv_h_id **text**;

​    _re_datas **varchar**; 

   _sn_status **text**;

 	_part_no	**text**;

  tmp_json **json**[];

  qty_txt **text**;

  _invp_no **text**;

 _is_hold_to_use **bool**;

**begin**  

  	--	INSERT INTO  a_test_log

​	--	(datas, source_name, crt_time)

​	--	VALUES(datas, 'af_sales_notice_bf', LOCALTIMESTAMP(0));  

​	

​	 _datas:=**json**(datas); 

​	-- _re_datas:=(_datas->'datas')::varchar; 

​    _sn_no:=_datas->'datas'->0->>'sn_no';

​    _cr_dlv_h_no :=_datas->'datas'->0->>'cr_dlv_h_no';

   

   **select** cr_dlv_h_id **into** _cr_dlv_h_id **from** cr_dlv_h **where** cr_dlv_h_no = _cr_dlv_h_no; 

  

   **select** ws.part_qty,ws.sn_status,ws.invp_no,ws.is_hold_to_use,ws.part_no **into** qty_txt,_sn_status,_invp_no,_is_hold_to_use,_part_no **from** wm_sn ws **where** ws.sn_no =  _sn_no;

   

​    **raise** **notice** '%',_sn_no;

​    **if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no)   **then** 

​    		 res:=**row**('false','条码'||_sn_no||'不存在');

​		**return**  **to_json**(res);  

​    **end** **if**; 

   

   --如果条码已上架，则不能发料必须先下架

​	**if** (_invp_no != **null** **or** _invp_no != '') **then**

​		res := **row**('false',**concat**('条码:',_sn_no,' 已上架:',_invp_no,'，如要出货请先下架！！！'));

​		**return** **to_json**(res);

​	**end** **if**;



​	--判断条码品号是否与出货单品号一致

​	**if** **not** **exists**(**select** 1 **from** cr_dlv_b cr **where** cr.cr_dlv_h_id = _cr_dlv_h_id **and** cr.part_no = _part_no **limit** 1) **then**

​		res := **row**('false',**concat**('扫码的条码品号:',_part_no,' 与送货单明细品号不一致！！！'));

​		**return** **to_json**(res);

​	**end** **if**;



​	--判断扫描的条码是否已经扫码退货

​	**if** **exists**(**select** 1 **from** wm_io **where** sn_no = _sn_no **and** wm_io_type_name **in**('采购退货','送检拒收')) **then**

​		res := **row**('false',**concat**('扫码的条码:',_sn_no,' 已经扫码退货了，不能在扫码出货给客户！！！'));

​		**return** **to_json**(res);

​	**end** **if**;



​	--如果条码已冻结，则不能发料，2022-12-16李辉提出客户赔偿报废物料走销售出货单所以不对报废过期物料做控制

​	/*if (_is_hold_to_use) then

​		res := row('false',concat('条码:',_sn_no,' 已冻结','，禁止出货！！！'));

​		return to_json(res);

​	end if;*/



​	--条码状态=作废，则不能发料，2022-12-16李辉提出客户赔偿报废物料走销售出货单所以不对报废过期物料做控制

​	/*if (_sn_status = '130') then

​		res := row('false',concat('条码:',_sn_no,' 已作废','，禁止出货！！！'));

​		return to_json(res);

​	end if;*/



​	--条码如果有发料记录则不能出货

​	**if** **exists**(**select** 1 **from** me_mtr_use_sn_part t,me_mtr_use_h h **where** t.me_mtr_use_h_id=h.me_mtr_use_h_id **and** h.me_mtr_use_h_io_type = '0' **and** t.sn_no = _sn_no) **then**

​		res := **row**('false',**concat**('条码:',_sn_no,' 已在领料单发过料','，禁止出货！！！'));

​		**return** **to_json**(res);

​	**end** **if**;



​	--获取出货单id

​	**select** cr_dlv_h_id **into** _cr_dlv_h_id **from** cr_dlv_h **where** cr_dlv_h_no = _cr_dlv_h_no;



​	--条码已经扫描出货不能再次扫描

​	**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** sn_no = _sn_no **and** cr_dlv_h_id = _cr_dlv_h_id) **then**

​		**select** h.cr_dlv_h_no **into** _cr_dlv_h_no **from** cr_dlv_sn_part pt,cr_dlv_h h **where** pt.cr_dlv_h_id = h.cr_dlv_h_id **and** pt.cr_dlv_h_id = _cr_dlv_h_id;

​		res := **row**('false',**concat**('条码：',_sn_no,' 已在出货单:',_cr_dlv_h_no,' 扫码出货不能再次扫描出货！！！'));

​		**return** **to_json**(res);

​	**end** **if**;





​	**if** **strpos**(qty_txt,'.')>0 **then**	

​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** part_no,part_name,'null' **as** part_spec,part_qty **from** wm_sn **where** sn_no=_sn_no) tmp;

​	**else**

​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** part_no,part_name,'null' **as** part_spec,part_qty **from** wm_sn **where** sn_no=_sn_no) tmp;

​	**end** **if**;

​	

​	**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);



   

​	**END**;

**$function$**

;
```



销售出库（发货通知单+条码）：af_wm_sales_out_notice_order

```sql
-- DROP FUNCTION public.af_wm_sales_out_notice_order(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_wm_sales_out_notice_order(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**  

/*

 \* 功能：销售出库扫描

 \* 描述：

 \* 时间：

 \* 开发者：LLY

 \* 

 \* 

 \* */

**declare**

​    _datas **json**;

​    results returntype;

   _user_no **text**;

   _crt_user **text**;

   _crt_user_no **text**;

   _crt_user_name **text**;

   _crt_host  **text**;

   _scan **json**;

​	_cr_dlv_h_id  **text** ;  -- 发货单 

​    _cr_dlv_h_no  **text**; -- 通知单

​    _sn_no	**text**; --条码编号

   _time **timestamp**;  

**begin**

​	  

--	    perform ulog('af_wm_sales_out_notice_order','datas',datas);

--	   results := row('false','为什么？');

--	   return to_json(results);

​             

​	   _datas := **json**(datas);

​	  _user_no:=_datas->>'user_no';

​	  _crt_host := _datas->>'tx_user_no';

​	  _datas:=_datas->>'datas';

​	  

​	 --获取操作人信息

​	  **select** u.user_id,u.user_no,u.user_name **into** _crt_user,_crt_user_no,_crt_user_name **from** ss_user u **where** u.user_no =  _user_no;

​	 ----

​	**for** _count **in** 0..**json_array_length**(_datas)-1 **loop**

​		_cr_dlv_h_no := _datas -> _count ->> 'cr_dlv_h_no';

​		_sn_no := _datas -> _count ->> 'sn_no';

​	

​		**select** cr_dlv_h_id **into** _cr_dlv_h_id **from** cr_dlv_h **where** cr_dlv_h_no = _cr_dlv_h_no;

​	

​		--写入销售出库记录标签

​		**insert** **into** 

​		cr_dlv_sn_part(

​			cr_dlv_sn_part_id,cr_dlv_h_id,sn_no,part_no,part_name,part_spec,part_unit,

​			invp_no,invp_name,part_qty,

​			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,

​			upd_time,upd_user,upd_user_no,upd_user_name,upd_host

​		)

​		**select**

​			af_auid(),_cr_dlv_h_id,_sn_no,**upper**(a.part_no),a.part_name,a.part_spec,un.qty_unit_no,

​			'null','null',a.part_qty,

​			**now**(),_crt_user,_crt_user_no,_crt_user_name,_crt_host,

​			**now**(),_crt_user,_crt_user_no,_crt_user_name,_crt_host

​		**from** wm_sn a

​		**left** **join** pd_part t **on** t.part_no = **upper**(a.part_no)

​		**left** **join** av_ss_qty_unit un **on** un.qty_unit_no = t.part_unit

​		**where** a.sn_no = _sn_no;

​		

​		--更新条码数量

​		**update** wm_sn  **set** part_qty = 0 **where** sn_no = _sn_no;	

​	

​	**end** **loop**;

​	

​	  

​	results := **row**('true','成功!');      

​    **return** **to_json**(results);   

**end**;  

 **$function$**

;
```

## 功能逻辑分析

### 一、数据逻辑分析

#### 1. 数据流转路径
```
扫描通知单号 → 验证单号存在性 → 扫描条码 → 多重验证 → 创建出库记录 → 清空条码库存
                                     ↓
                              条码验证包括：
                              - 上架状态验证
                              - 品号匹配验证
                              - 退货状态验证
                              - 发料记录验证
                              - 重复扫描验证
```

#### 2. 核心数据处理
- **通知单校验**（`af_sales_notice_head`）：验证出库单号存在性
- **条码校验**（`af_sales_notice_bf`）：多维度验证条码合法性
- **出库执行**（`af_wm_sales_out_notice_order`）：创建出库记录并清空库存

### 二、业务逻辑分析

#### 1. 业务场景
- **另一种有序列号出库方式**
- 通过扫描通知单+条码的方式完成出库
- 适用于需要逐个扫描但流程相对简单的场景

#### 2. 关键业务规则

##### 2.1 条码验证规则（严格）
```sql
-- 1. 条码必须存在
if not exists(select 1 from wm_sn where sn_no=_sn_no) then
    -- 报错：条码不存在
end if;

-- 2. 条码不能已上架
if (_invp_no != null or _invp_no != '') then
    -- 报错：已上架，需先下架
end if;

-- 3. 品号必须匹配
if not exists(select 1 from cr_dlv_b where cr_dlv_h_id = _cr_dlv_h_id 
    and part_no = _part_no) then
    -- 报错：品号不一致
end if;

-- 4. 不能是退货条码
if exists(select 1 from wm_io where sn_no = _sn_no 
    and wm_io_type_name in('采购退货','送检拒收')) then
    -- 报错：已退货
end if;

-- 5. 不能有发料记录
if exists(select 1 from me_mtr_use_sn_part t, me_mtr_use_h h 
    where t.me_mtr_use_h_id=h.me_mtr_use_h_id 
    and h.me_mtr_use_h_io_type = '0' 
    and t.sn_no = _sn_no) then
    -- 报错：已发料
end if;

-- 6. 不能重复扫描
if exists(select 1 from cr_dlv_sn_part 
    where sn_no = _sn_no and cr_dlv_h_id = _cr_dlv_h_id) then
    -- 报错：已扫描
end if;
```

##### 2.2 特殊处理
- **冻结物料**：原有冻结验证被注释（客户赔偿报废物料需要出库）
- **作废状态**：原有作废验证被注释（同上原因）

#### 3. 出库处理逻辑
```sql
-- 1. 创建出库记录
insert into cr_dlv_sn_part(...) 
select ... from wm_sn where sn_no = _sn_no;

-- 2. 清空条码库存（特殊处理）
update wm_sn set part_qty = 0 where sn_no = _sn_no;
```

### 三、代码逻辑分析

#### 1. 模块化设计
- **af_sales_notice_head**：单独验证通知单
- **af_sales_notice_bf**：单独验证条码
- **af_wm_sales_out_notice_order**：执行出库

#### 2. 批量处理支持
```sql
-- 支持批量扫描多个条码
for _count in 0..json_array_length(_datas)-1 loop
    _cr_dlv_h_no := _datas -> _count ->> 'cr_dlv_h_no';
    _sn_no := _datas -> _count ->> 'sn_no';
    -- 处理每个条码
end loop;
```

#### 3. 特殊的库存处理
```sql
-- 不是物理删除或转移，而是将数量置0
update wm_sn set part_qty = 0 where sn_no = _sn_no;
```
**问题**：这种处理方式可能导致数据不一致，条码还在但数量为0

### 四、表关联逻辑分析

#### 1. 核心表关系
```mermaid
graph TD
    A[cr_dlv_h 出库单头] -->|cr_dlv_h_id| B[cr_dlv_b 出库单明细]
    C[wm_sn 序列号库存] -->|验证| D{多重验证}
    D -->|通过| E[cr_dlv_sn_part 出库记录]
    F[wm_io 出入库记录] -->|退货验证| D
    G[me_mtr_use_sn_part 发料记录] -->|发料验证| D
    H[pd_part 物料主数据] -->|单位信息| E
    
    C -->|清空数量| I[库存更新]
    
    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#ffcc99
    style E fill:#99ff99
```

#### 2. 验证涉及的表
- `wm_sn`：条码库存信息
- `wm_io`：出入库记录（验证退货）
- `me_mtr_use_sn_part` + `me_mtr_use_h`：发料记录
- `cr_dlv_sn_part`：防止重复扫描

#### 3. 数据一致性问题
- 条码数量置0但记录还在，可能导致查询混乱
- 没有更新条码状态，仅清空数量
- 没有更新出库单的状态和数量

### 五、与其他模块的对比

| 特性 | 销售出库拣货 | 销售出库（通知单+条码） | 无序列号出库 |
|------|------------|----------------------|------------|
| 扫描方式 | 单号+条码 | 通知单+条码 | 仅单号 |
| 流程复杂度 | 复杂（多环节） | 中等 | 简单 |
| 状态流转 | 完整 | 缺失 | 完整 |
| 库存处理 | 更新状态 | 清空数量 | 扣减数量 |
| 验证严格度 | 中等 | 最严格 | 最宽松 |

### 六、潜在问题与优化建议

#### 1. 库存处理问题
**现状**：仅将数量置0，不更新状态
**建议**：
```sql
-- 更新状态而非仅清空数量
update wm_sn set 
    sn_status = '900',  -- 已出库状态
    sn_status_name = '已出库',
    part_qty = 0,
    upd_time = now()
where sn_no = _sn_no;
```

#### 2. 单据状态缺失
**问题**：没有更新出库单的状态和实际数量
**建议**：
```sql
-- 更新明细实际数量
update cr_dlv_b set 
    cr_dlv_qty = cr_dlv_qty + 扫描数量
where ...;

-- 检查并更新单头状态
if 所有明细完成 then
    update cr_dlv_h set cr_dlv_h_rmk6 = '发货完成';
end if;
```

#### 3. 验证逻辑优化
- 将被注释的冻结/作废验证改为可配置
- 增加验证结果的详细日志记录

#### 4. 性能优化
- 批量验证优化，减少数据库查询次数
- 考虑使用事务批量提交

### 七、业务定位分析

这个模块的设计初衷可能是：
1. **简化版的有序列号出库**：跳过拣货环节，直接扫码出库
2. **特殊场景支持**：如客户赔偿、报废品出库等
3. **快速出库**：适合小批量、紧急出库的场景

但由于缺少状态管理和数量统计，可能无法与SAP接口正常对接，需要进一步完善。

