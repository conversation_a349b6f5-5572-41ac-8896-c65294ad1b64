单据号校验：af_query_wms_sales_outbound_list_sn

```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询待绑定信息的产品（已经拣货完成）

 \* 描述：

 \* 时间:

 \* 开发者：

 */



​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**BEGIN**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';





​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** cb.sn_no,cb.part_qty,cb.part_no 

​			**from** cr_dlv_h ca 

​			**left** **join** cr_dlv_sn_part cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			**where** ca.cr_dlv_h_no=_bill_no **and** **coalesce**(ca.cr_dlv_h_rmk6,'')='拣货完成'

​				**and** **coalesce**(cb.cr_dlv_sn_part_rmk1,'')='') tmp;



​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);

​	**END**;

**$function$**

;
```





销售出库扫描（品质）：af_pda_wms_sales_outbound_inspection



```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_inspection(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_outbound_inspection(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能  ：出货检验扫描（OQC扫描）

 \* 描述  ：销售出库OQC检验合格后，扫描绑定HW的相关信息; HW_SN 包含 HW_CARTON_SN 与 19_CODE

 \* 时间  ：

 \* 开发者：

 */

​	**declare** 

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;

​	

​		_bill_no **text**;

​		_bill_id **text**;

​		_client_no **text**;

​		_sn_no **text**;

​		_part_no **text**;

​	

​		_prod_09_code **text**;

​		_hw_sn **text**;

​		_hw_carton_sn **text**;

​	

​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;	

​		res returntype;

​	**begin**

​		json_datas := **json**(datas);	

​		_user_no := json_datas->>'user_no';

​	

​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'jy_sn_no';

​		_prod_09_code := json_datas->>'jy_09_code';

​		_hw_sn := json_datas->>'hw_sn';

​		_hw_carton_sn := json_datas->>'hw_carton_sn';

​	

​		**select** cr_dlv_h_id,client_no **into** _bill_id,_client_no **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no;

​	

​		--insert into public.a_test_log values(datas, 'pda_wms_sales_outbound_inspection', localtimestamp);

​		--	res := row('true', '销售出库检验扫描完成');

​		--return to_json(res);

​	

​		**select** part_no **into** _part_no **from** wm_sn **where** sn_no=_sn_no;

​		

​		--------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no **and** **coalesce**(cr_dlv_h_rmk6,'')='拣货完成') **then**

​			res := **row**('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no) **then**

​			res := **row**('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no 

​				**and** **coalesce**(cr_dlv_sn_part_rmk1,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk2,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk3,'')='') **then**

​			res := **row**('false', '扫描产品已经绑定信息，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		-------------------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no **and** sn_status='810') **then** 

​			--res := row('false', '此产品不是待OQC检验状态，请核对。');

​			--return to_json(res);

​		**end** **if**;

​		----------------------------------------------------

​		/*if strpos(_sn_no, concat('19',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品19码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;

​	

​		if strpos(_prod_09_code, concat('09',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品09码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;*/

​		

​		**if** **strpos**(_hw_sn, _hw_carton_sn)=0 **then**

​			res := **row**('false', '扫描华为二维码与 HW外箱码不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **strpos**(_hw_sn, _sn_no)=0 **then**

​			res := **row**('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​		---------------------------------------------------	

​		/*

​		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then

​			res := row('false','此产品没有销售出库拣货，不能做出货检验。');

​			return to_json(res);

​		end if;

​		

​		------无需生成OQC出货检验单--------

​		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id

​						where a.move_order_id=_bill_id and b.sn_no=_sn_no) then

​			res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');

​			return to_json(res);

​		end if;

​		*/

​		

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk2=_hw_sn) **then**

​			res := **row**('false', '此华为SN已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk3=_hw_carton_sn) **then**

​			res := **row**('false', '此华为卡通箱号已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		--------------------------------------------------------------------------------------------



​		**update** public.cr_dlv_sn_part **set** cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,

​			upd_time=**localtimestamp**

​		**where** sn_no=_sn_no **and** cr_dlv_h_id=_bill_id;	

​	

​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** **coalesce**(cr_dlv_sn_part_rmk1,'')='') **then**

​			**update** public.cr_dlv_h **set** cr_dlv_h_rmk6='OQC绑定信息完成'

​			**where** cr_dlv_h_no=_bill_no;

​		**end** **if**;

​	

​		/*

​		update public.wm_sn set sn_status='820',sn_status_name='OQC检验',upd_time=localtimestamp

​		where sn_no=_sn_no;

​		*/

​		res := **row**('true', '销售出库检验扫描完成');

​		**return** **to_json**(res);



​	**EXCEPTION** **WHEN** **OTHERS** **THEN** 

​		**GET** STACKED **DIAGNOSTICS** 

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);	



​	**END**;

**$function$**

;
```



```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询待绑定信息的产品（已经拣货完成）

 \* 描述：

 \* 时间:

 \* 开发者：

 */



​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**BEGIN**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';





​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** cb.sn_no,cb.part_qty,cb.part_no 

​			**from** cr_dlv_h ca 

​			**left** **join** cr_dlv_sn_part cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			**where** ca.cr_dlv_h_no=_bill_no **and** **coalesce**(ca.cr_dlv_h_rmk6,'')='拣货完成'

​				**and** **coalesce**(cb.cr_dlv_sn_part_rmk1,'')='') tmp;



​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);

​	**END**;

**$function$**

;
```

```sql


-- DROP FUNCTION public.af_pda_wms_sales_outbound_inspection(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_outbound_inspection(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能  ：出货检验扫描（OQC扫描）

 \* 描述  ：销售出库OQC检验合格后，扫描绑定HW的相关信息; HW_SN 包含 HW_CARTON_SN 与 19_CODE

 \* 时间  ：

 \* 开发者：

 */

​	**declare** 

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;

​	

​		_bill_no **text**;

​		_bill_id **text**;

​		_client_no **text**;

​		_sn_no **text**;

​		_part_no **text**;

​	

​		_prod_09_code **text**;

​		_hw_sn **text**;

​		_hw_carton_sn **text**;

​	

​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;	

​		res returntype;

​	**begin**

​		json_datas := **json**(datas);	

​		_user_no := json_datas->>'user_no';

​	

​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'jy_sn_no';

​		_prod_09_code := json_datas->>'jy_09_code';

​		_hw_sn := json_datas->>'hw_sn';

​		_hw_carton_sn := json_datas->>'hw_carton_sn';

​	

​		**select** cr_dlv_h_id,client_no **into** _bill_id,_client_no **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no;

​	

​		--insert into public.a_test_log values(datas, 'pda_wms_sales_outbound_inspection', localtimestamp);

​		--	res := row('true', '销售出库检验扫描完成');

​		--return to_json(res);

​	

​		**select** part_no **into** _part_no **from** wm_sn **where** sn_no=_sn_no;

​		

​		--------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no **and** **coalesce**(cr_dlv_h_rmk6,'')='拣货完成') **then**

​			res := **row**('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no) **then**

​			res := **row**('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no 

​				**and** **coalesce**(cr_dlv_sn_part_rmk1,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk2,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk3,'')='') **then**

​			res := **row**('false', '扫描产品已经绑定信息，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		-------------------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no **and** sn_status='810') **then** 

​			--res := row('false', '此产品不是待OQC检验状态，请核对。');

​			--return to_json(res);

​		**end** **if**;

​		----------------------------------------------------

​		/*if strpos(_sn_no, concat('19',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品19码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;

​	

​		if strpos(_prod_09_code, concat('09',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品09码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;*/

​		

​		**if** **strpos**(_hw_sn, _hw_carton_sn)=0 **then**

​			res := **row**('false', '扫描华为二维码与 HW外箱码不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **strpos**(_hw_sn, _sn_no)=0 **then**

​			res := **row**('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​		---------------------------------------------------	

​		/*

​		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then

​			res := row('false','此产品没有销售出库拣货，不能做出货检验。');

​			return to_json(res);

​		end if;

​		

​		------无需生成OQC出货检验单--------

​		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id

​						where a.move_order_id=_bill_id and b.sn_no=_sn_no) then

​			res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');

​			return to_json(res);

​		end if;

​		*/

​		

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk2=_hw_sn) **then**

​			res := **row**('false', '此华为SN已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk3=_hw_carton_sn) **then**

​			res := **row**('false', '此华为卡通箱号已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		--------------------------------------------------------------------------------------------



​		**update** public.cr_dlv_sn_part **set** cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,

​			upd_time=**localtimestamp**

​		**where** sn_no=_sn_no **and** cr_dlv_h_id=_bill_id;	

​	

​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** **coalesce**(cr_dlv_sn_part_rmk1,'')='') **then**

​			**update** public.cr_dlv_h **set** cr_dlv_h_rmk6='OQC绑定信息完成'

​			**where** cr_dlv_h_no=_bill_no;

​		**end** **if**;

​	

​		/*

​		update public.wm_sn set sn_status='820',sn_status_name='OQC检验',upd_time=localtimestamp

​		where sn_no=_sn_no;

​		*/

​		res := **row**('true', '销售出库检验扫描完成');

​		**return** **to_json**(res);



​	**EXCEPTION** **WHEN** **OTHERS** **THEN** 

​		**GET** STACKED **DIAGNOSTICS** 

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);	



​	**END**;

**$function$**

;
```





## 功能逻辑深度分析

### 一、数据逻辑深度分析

#### 1.1 数据流转的完整生命周期
```mermaid
graph TD
    A[销售订单] --> B[出库单生成]
    B --> C[拣货作业]
    C --> D[拣货完成状态]
    D --> E[品质扫描准备]
    E --> F[逐个产品绑定]
    F --> G[全部绑定完成]
    G --> H[发货准备]

    subgraph "数据状态变化"
        I[cr_dlv_h_rmk6: 空] --> J[cr_dlv_h_rmk6: 拣货完成]
        J --> K[cr_dlv_sn_part_rmk1/2/3: 逐步填充]
        K --> L[cr_dlv_h_rmk6: OQC绑定信息完成]
    end
```

#### 1.2 数据一致性的多维度验证
华为标识的层级结构验证体现了供应链的完整追溯要求：
```sql
-- 数据包含关系验证（体现了华为标识的层级结构）
if strpos(_hw_sn, _hw_carton_sn)=0 then
    res := row('false', '扫描华为二维码与 HW外箱码不一致。');
    return to_json(res);
end if;

if strpos(_hw_sn, _sn_no)=0 then
    res := row('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');
    return to_json(res);
end if;
```

**数据逻辑特点**：
- **数据编码规则**：华为的二维码设计包含了多层信息，体现了供应链的层级关系
- **数据完整性约束**：通过字符串包含关系确保标识的一致性，这是一种业务层面的外键约束
- **数据冗余设计**：三个备注字段存储相关但不同的标识，便于不同场景的查询和验证

#### 1.3 数据状态的原子性管理
```sql
-- 原子性状态更新：只有全部产品完成绑定才更新单据状态
if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_sn_part_rmk1,'')='') then
    update public.cr_dlv_h set cr_dlv_h_rmk6='OQC绑定信息完成'
    where cr_dlv_h_no=_bill_no;
end if;
```

#### 1.4 核心数据处理功能
- **查询功能**(`af_query_wms_sales_outbound_list_sn`)：查询拣货完成但未绑定品质信息的产品
- **绑定功能**(`af_pda_wms_sales_outbound_inspection`)：扫描并绑定华为相关信息

### 二、业务逻辑深度分析

#### 2.1 华为客户特殊业务需求的实现
**业务背景**：华为作为大客户，对产品追溯有特殊要求，需要建立从外箱到单品的完整追溯链。

**业务规则实现**：
1. **三重标识绑定**：09码（内部标识）+ HW_SN（华为二维码）+ HW外箱码
2. **层级包含验证**：确保标识之间的逻辑关系正确
3. **唯一性保证**：防止标识重复使用造成追溯混乱

**OQC（出货品质检验）扫描绑定场景**：
- 针对华为客户的特殊要求，需要绑定额外的标识信息
- 在拣货完成后、发货前的品质检验环节
- 建立完整的产品追溯链条

#### 2.2 品质管控的业务流程设计
```sql
-- 业务状态前置验证
if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='拣货完成') then
    res := row('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');
    return to_json(res);
end if;
```

**业务流程特点**：
- **业务节点控制**：严格按照"拣货→品质→发货"的顺序执行
- **质量门控制**：每个环节都有明确的准入条件和完成标准
- **异常处理机制**：对不符合业务规则的操作及时阻止并给出明确提示

#### 2.3 关键业务规则详解

##### 2.3.1 前置条件验证
- 出库单必须处于"拣货完成"状态（`cr_dlv_h_rmk6='拣货完成'`）
- 产品必须已经完成拣货（存在于`cr_dlv_sn_part`表中）
- 产品未绑定过品质信息（备注字段为空）

##### 2.3.2 数据一致性验证
```sql
-- HW二维码必须包含外箱码
if strpos(_hw_sn, _hw_carton_sn)=0 then
    res := row('false', '扫描华为二维码与 HW外箱码不一致。');
end if;

-- HW二维码必须包含产品19码
if strpos(_hw_sn, _sn_no)=0 then
    res := row('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');
end if;
```

##### 2.3.3 唯一性验证
- 同一HW_SN不能重复绑定
- 同一HW外箱码不能重复绑定

#### 2.4 被注释业务逻辑的影响分析
```sql
-- 被注释的库存状态验证
/*if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then
    res := row('false', '此产品不是待OQC检验状态，请核对。');
    return to_json(res);
end if;*/

-- 被注释的质检单关联验证
/*if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
                where a.move_order_id=_bill_id and b.sn_no=_sn_no) then
    res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');
    return to_json(res);
end if;*/
```

**业务风险**：
- 可能允许非正常状态的产品进行绑定
- 缺少与质检系统的关联验证，降低了质量管控的严格性

#### 2.5 状态流转机制
- **单据状态**：拣货完成 → OQC绑定信息完成（所有产品绑定后）
- **库存状态**：原设计有更新为820（OQC检验），但代码中已注释
- **渐进式完成**：支持部分绑定状态，便于分批操作

### 三、代码逻辑深度分析

#### 3.1 函数设计模式分析
**查询函数的设计特点**：
```sql
-- af_query_wms_sales_outbound_list_sn
select array_agg(row_to_json(tmp)) into tmp_json
from (select cb.sn_no,cb.part_qty,cb.part_no
    from cr_dlv_h ca
    left join cr_dlv_sn_part cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id
    where ca.cr_dlv_h_no=_bill_no and coalesce(ca.cr_dlv_h_rmk6,'')='拣货完成'
        and coalesce(cb.cr_dlv_sn_part_rmk1,'')='') tmp;
```

**代码特点**：
- **JSON聚合**：使用`array_agg(row_to_json())`将查询结果转换为JSON数组
- **状态过滤**：同时检查单据状态和明细绑定状态
- **空值处理**：使用`coalesce`函数处理NULL值
- **双函数设计**：查询函数获取待绑定列表，操作函数执行具体绑定

#### 3.2 数据验证层次结构
```sql
-- 第一层：业务状态验证
if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='拣货完成') then
    res := row('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');
    return to_json(res);
end if;

-- 第二层：数据关联验证
if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then
    res := row('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');
    return to_json(res);
end if;

-- 第三层：重复绑定验证
if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no
        and coalesce(cr_dlv_sn_part_rmk1,'')='' and coalesce(cr_dlv_sn_part_rmk2,'')='' and coalesce(cr_dlv_sn_part_rmk3,'')='') then
    res := row('false', '扫描产品已经绑定信息，不能二次绑定。');
    return to_json(res);
end if;
```

#### 3.3 异常处理机制分析
```sql
EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;

    _err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
    res := row('false',_err_msg);
    return to_json(res);
```

**代码问题**：
- 错误信息格式化中重复使用了`_err_msg_text`，应该是`_err_pg_detail`
- 缺少具体的错误分类处理

#### 3.4 事务控制和数据更新逻辑
```sql
-- 更新明细绑定信息
update public.cr_dlv_sn_part set cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,
    upd_time=localtimestamp
where sn_no=_sn_no and cr_dlv_h_id=_bill_id;

-- 条件性更新单据状态
if not exists(select distinct 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_sn_part_rmk1,'')='') then
    update public.cr_dlv_h set cr_dlv_h_rmk6='OQC绑定信息完成'
    where cr_dlv_h_no=_bill_no;
end if;
```

**代码逻辑优势**：
- **精确更新**：通过双重条件确保更新的准确性
- **状态同步**：明细完成后自动更新单据状态
- **时间戳记录**：记录操作时间便于追溯

#### 3.5 绑定逻辑的完整步骤
1. **验证出库单状态**：确保处于拣货完成状态
2. **验证产品归属**：确保产品属于该出库单
3. **验证绑定状态**：确保产品未被绑定
4. **验证HW信息一致性**：确保华为标识的层级关系正确
5. **验证唯一性**：确保华为标识未被重复使用
6. **更新绑定信息**：写入三个备注字段
7. **更新单据状态**：判断是否全部完成并更新状态

#### 3.6 潜在代码问题
- **状态验证被注释**：原有的库存状态验证（810）被注释，可能导致非拣货状态的产品也能绑定
- **质检单关联被注释**：与`qm_si_lot_h`质检单的关联验证被注释，降低了数据完整性要求
- **异常处理不完善**：错误信息格式化存在问题，缺少分类处理

### 四、表关联逻辑深度分析

#### 4.1 核心表结构关系图
```mermaid
erDiagram
    cr_dlv_h ||--o{ cr_dlv_sn_part : "1对多"
    wm_sn ||--o{ cr_dlv_sn_part : "1对多"
    qm_si_lot_h ||--o{ qm_si_lot_b_sn : "1对多"
    cr_dlv_h ||--o{ qm_si_lot_h : "1对多(已注释)"

    cr_dlv_h {
        varchar cr_dlv_h_id PK
        varchar cr_dlv_h_no UK "出库单号"
        varchar cr_dlv_h_rmk6 "状态标识"
        varchar client_no "客户编号"
    }

    cr_dlv_sn_part {
        varchar cr_dlv_h_id FK
        varchar sn_no FK
        varchar part_no "产品编号"
        decimal part_qty "数量"
        varchar cr_dlv_sn_part_rmk1 "09码"
        varchar cr_dlv_sn_part_rmk2 "HW_SN"
        varchar cr_dlv_sn_part_rmk3 "HW外箱码"
        varchar cr_dlv_sn_part_rmk6 "关联ID"
    }

    wm_sn {
        varchar sn_no PK
        varchar part_no "产品编号"
        varchar sn_status "库存状态"
        varchar sn_status_name "状态名称"
    }
```

![1751457800879](D:\金洋\成品出入库记录\出库\assets\1751457800879.png)

#### 4.2 关联查询的性能分析

```sql
-- 查询函数的关联逻辑
from cr_dlv_h ca
left join cr_dlv_sn_part cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id
where ca.cr_dlv_h_no=_bill_no and coalesce(ca.cr_dlv_h_rmk6,'')='拣货完成'
    and coalesce(cb.cr_dlv_sn_part_rmk1,'')=''
```

**关联逻辑特点**：
- **左连接设计**：确保即使没有明细记录也能查询到单据信息
- **复合条件过滤**：同时检查主表状态和明细表绑定状态
- **索引需求**：需要在`cr_dlv_h_no`、`cr_dlv_h_rmk6`、`cr_dlv_sn_part_rmk1`上建立索引

#### 4.3 关键字段详细说明
- **`cr_dlv_sn_part_rmk1`**：存储产品09码（华为内部标识）
- **`cr_dlv_sn_part_rmk2`**：存储华为SN（包含多个信息的二维码）
- **`cr_dlv_sn_part_rmk3`**：存储华为外箱码（包装层级标识）
- **`cr_dlv_sn_part_rmk6`**：存储关联的明细ID（从拣货环节继承）
- **`cr_dlv_h_rmk6`**：存储单据状态（拣货完成→OQC绑定信息完成）

#### 4.4 数据完整性约束分析
**现有约束**：
- 通过应用层验证确保数据一致性
- 使用唯一性检查防止重复绑定
- 事务控制确保数据更新的原子性

**缺失约束**：
- 缺少数据库层面的外键约束
- 缺少字段级别的检查约束
- 缺少数据类型和长度限制

#### 4.5 扩展性设计分析
**备注字段的使用模式**：
- `cr_dlv_sn_part_rmk1-3`：用于存储华为特殊标识
- `cr_dlv_sn_part_rmk6`：用于存储关联ID
- `cr_dlv_h_rmk6`：用于存储单据状态

**设计优势**：
- 灵活性高，便于适配不同客户需求
- 无需修改表结构即可支持新的业务场景
- 便于快速实现客户定制化要求

**设计劣势**：
- 字段语义不明确，维护困难
- 缺少数据类型约束，容易出现数据质量问题
- 不利于数据库优化和索引建立

#### 4.6 表关联的业务逻辑
```mermaid
graph TD
    A[cr_dlv_h 出库单头] -->|cr_dlv_h_id| B[cr_dlv_sn_part 拣货记录]
    C[wm_sn 序列号库存] -->|sn_no| B
    B -->|绑定HW信息| D[品质信息字段]
    E[qm_si_lot_h 质检单头] -.->|已注释| B
    F[qm_si_lot_b_sn 质检明细] -.->|已注释| B

    D --> |rmk1| G[09码]
    D --> |rmk2| H[HW_SN]
    D --> |rmk3| I[HW外箱码]

    style A fill:#ff9999
    style B fill:#99ff99
    style C fill:#ffcc99
    style E fill:#cccccc
    style F fill:#cccccc
```

### 五、实际应用场景分析

#### 5.1 典型操作流程
```
1. 仓库完成拣货 → cr_dlv_h_rmk6='拣货完成'
2. 品质人员扫描出库单号 → 调用查询函数获取待绑定产品列表
3. 逐个扫描产品和华为标识 → 调用绑定函数
4. 系统验证并绑定 → 更新cr_dlv_sn_part备注字段
5. 全部绑定完成 → cr_dlv_h_rmk6='OQC绑定信息完成'
```

#### 5.2 异常处理场景
- **扫描错误**：通过一致性验证及时发现并阻止
- **重复操作**：通过唯一性验证防止重复绑定
- **状态不符**：通过状态验证确保操作时机正确
- **数据不匹配**：通过包含关系验证确保标识正确性

#### 5.3 系统设计的优势与不足

**优势**：
1. **完整的验证体系**：从业务状态到数据一致性的多层验证
2. **原子性操作**：单个产品的绑定是原子的，失败不影响其他产品
3. **灵活的状态管理**：支持部分绑定状态，便于分批操作
4. **客户特殊需求适配**：针对华为的特殊要求定制化实现

**不足与风险**：
1. **关键验证被注释**：库存状态验证和质检关联验证被注释，降低了数据完整性
2. **错误处理不够细化**：异常处理比较粗糙，不利于问题定位
3. **缺乏操作日志**：没有详细的操作记录，不利于问题追溯

### 六、综合优化建议

#### 6.1 数据层优化
1. **建立合适的索引**：
   ```sql
   CREATE INDEX idx_cr_dlv_h_no_rmk6 ON cr_dlv_h(cr_dlv_h_no, cr_dlv_h_rmk6);
   CREATE INDEX idx_cr_dlv_sn_part_rmk ON cr_dlv_sn_part(cr_dlv_h_id, cr_dlv_sn_part_rmk1);
   CREATE INDEX idx_cr_dlv_sn_part_hw ON cr_dlv_sn_part(cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3);
   ```

2. **增加数据约束**：
   ```sql
   ALTER TABLE cr_dlv_h ADD CONSTRAINT chk_rmk6_status
   CHECK (cr_dlv_h_rmk6 IN ('拣货完成', 'OQC绑定信息完成', ''));

   ALTER TABLE cr_dlv_sn_part ADD CONSTRAINT chk_rmk_consistency
   CHECK ((cr_dlv_sn_part_rmk1 IS NULL AND cr_dlv_sn_part_rmk2 IS NULL AND cr_dlv_sn_part_rmk3 IS NULL) OR
          (cr_dlv_sn_part_rmk1 IS NOT NULL AND cr_dlv_sn_part_rmk2 IS NOT NULL AND cr_dlv_sn_part_rmk3 IS NOT NULL));
   ```

#### 6.2 业务逻辑优化
1. **恢复关键验证**：
   ```sql
   -- 恢复库存状态验证
   if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then
       res := row('false', '此产品不是待OQC检验状态，请核对。');
       return to_json(res);
   end if;

   -- 恢复质检关联验证
   if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
                   where a.move_order_id=_bill_id and b.sn_no=_sn_no) then
       res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');
       return to_json(res);
   end if;
   ```

2. **增加配置化支持**：将客户特殊需求配置化，提高系统灵活性
3. **完善异常处理**：提供更详细和友好的错误信息

#### 6.3 代码逻辑优化
1. **修复错误处理**：
   ```sql
   _err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_pg_detail);
   ```

2. **增加日志记录**：
   ```sql
   INSERT INTO operation_log(operation_type, bill_no, sn_no, user_no, operation_time, operation_result)
   VALUES('OQC_BINDING', _bill_no, _sn_no, _user_no, localtimestamp, 'SUCCESS');
   ```

3. **支持批量操作**：提高大批量绑定的效率

#### 6.4 表设计优化
1. **规范化备注字段**：考虑将备注字段拆分为专门的扩展表
2. **增加审计字段**：记录创建人、修改人等信息
3. **建立数据字典**：明确各字段的业务含义和使用规范

#### 6.5 性能优化建议
- 对高频查询字段建立复合索引
- 批量处理多个产品的绑定操作
- 优化查询语句，减少不必要的表连接

#### 6.6 业务功能增强
- 支持解绑功能，处理扫描错误的情况
- 增加扫描历史记录和操作轨迹
- 支持不同客户的不同绑定要求配置化
- 集成条码打印功能
- 支持移动端离线操作

### 七、总结

这个PDA销售出库扫描(品质)系统体现了制造业WMS中品质管控的复杂性，特别是针对大客户特殊要求的精细化管理。系统通过多层验证机制确保了数据的准确性和业务流程的规范性，但在数据完整性验证、异常处理和性能优化方面还有进一步提升的空间。

**核心价值**：
- 建立了完整的产品追溯链条
- 满足了华为客户的特殊业务需求
- 实现了品质管控的数字化和标准化

**改进方向**：
- 恢复被注释的关键验证逻辑
- 完善错误处理和日志记录
- 优化数据库设计和性能
- 增强系统的可配置性和扩展性