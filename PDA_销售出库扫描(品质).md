单据号校验：af_query_wms_sales_outbound_list_sn

```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询待绑定信息的产品（已经拣货完成）

 \* 描述：

 \* 时间:

 \* 开发者：

 */



​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**BEGIN**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';





​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** cb.sn_no,cb.part_qty,cb.part_no 

​			**from** cr_dlv_h ca 

​			**left** **join** cr_dlv_sn_part cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			**where** ca.cr_dlv_h_no=_bill_no **and** **coalesce**(ca.cr_dlv_h_rmk6,'')='拣货完成'

​				**and** **coalesce**(cb.cr_dlv_sn_part_rmk1,'')='') tmp;



​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);

​	**END**;

**$function$**

;
```





销售出库扫描（品质）：af_pda_wms_sales_outbound_inspection



```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_inspection(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_outbound_inspection(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能  ：出货检验扫描（OQC扫描）

 \* 描述  ：销售出库OQC检验合格后，扫描绑定HW的相关信息; HW_SN 包含 HW_CARTON_SN 与 19_CODE

 \* 时间  ：

 \* 开发者：

 */

​	**declare** 

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;

​	

​		_bill_no **text**;

​		_bill_id **text**;

​		_client_no **text**;

​		_sn_no **text**;

​		_part_no **text**;

​	

​		_prod_09_code **text**;

​		_hw_sn **text**;

​		_hw_carton_sn **text**;

​	

​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;	

​		res returntype;

​	**begin**

​		json_datas := **json**(datas);	

​		_user_no := json_datas->>'user_no';

​	

​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'jy_sn_no';

​		_prod_09_code := json_datas->>'jy_09_code';

​		_hw_sn := json_datas->>'hw_sn';

​		_hw_carton_sn := json_datas->>'hw_carton_sn';

​	

​		**select** cr_dlv_h_id,client_no **into** _bill_id,_client_no **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no;

​	

​		--insert into public.a_test_log values(datas, 'pda_wms_sales_outbound_inspection', localtimestamp);

​		--	res := row('true', '销售出库检验扫描完成');

​		--return to_json(res);

​	

​		**select** part_no **into** _part_no **from** wm_sn **where** sn_no=_sn_no;

​		

​		--------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no **and** **coalesce**(cr_dlv_h_rmk6,'')='拣货完成') **then**

​			res := **row**('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no) **then**

​			res := **row**('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no 

​				**and** **coalesce**(cr_dlv_sn_part_rmk1,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk2,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk3,'')='') **then**

​			res := **row**('false', '扫描产品已经绑定信息，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		-------------------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no **and** sn_status='810') **then** 

​			--res := row('false', '此产品不是待OQC检验状态，请核对。');

​			--return to_json(res);

​		**end** **if**;

​		----------------------------------------------------

​		/*if strpos(_sn_no, concat('19',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品19码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;

​	

​		if strpos(_prod_09_code, concat('09',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品09码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;*/

​		

​		**if** **strpos**(_hw_sn, _hw_carton_sn)=0 **then**

​			res := **row**('false', '扫描华为二维码与 HW外箱码不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **strpos**(_hw_sn, _sn_no)=0 **then**

​			res := **row**('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​		---------------------------------------------------	

​		/*

​		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then

​			res := row('false','此产品没有销售出库拣货，不能做出货检验。');

​			return to_json(res);

​		end if;

​		

​		------无需生成OQC出货检验单--------

​		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id

​						where a.move_order_id=_bill_id and b.sn_no=_sn_no) then

​			res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');

​			return to_json(res);

​		end if;

​		*/

​		

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk2=_hw_sn) **then**

​			res := **row**('false', '此华为SN已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk3=_hw_carton_sn) **then**

​			res := **row**('false', '此华为卡通箱号已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		--------------------------------------------------------------------------------------------



​		**update** public.cr_dlv_sn_part **set** cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,

​			upd_time=**localtimestamp**

​		**where** sn_no=_sn_no **and** cr_dlv_h_id=_bill_id;	

​	

​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** **coalesce**(cr_dlv_sn_part_rmk1,'')='') **then**

​			**update** public.cr_dlv_h **set** cr_dlv_h_rmk6='OQC绑定信息完成'

​			**where** cr_dlv_h_no=_bill_no;

​		**end** **if**;

​	

​		/*

​		update public.wm_sn set sn_status='820',sn_status_name='OQC检验',upd_time=localtimestamp

​		where sn_no=_sn_no;

​		*/

​		res := **row**('true', '销售出库检验扫描完成');

​		**return** **to_json**(res);



​	**EXCEPTION** **WHEN** **OTHERS** **THEN** 

​		**GET** STACKED **DIAGNOSTICS** 

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);	



​	**END**;

**$function$**

;
```



```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询待绑定信息的产品（已经拣货完成）

 \* 描述：

 \* 时间:

 \* 开发者：

 */



​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**BEGIN**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';





​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** cb.sn_no,cb.part_qty,cb.part_no 

​			**from** cr_dlv_h ca 

​			**left** **join** cr_dlv_sn_part cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			**where** ca.cr_dlv_h_no=_bill_no **and** **coalesce**(ca.cr_dlv_h_rmk6,'')='拣货完成'

​				**and** **coalesce**(cb.cr_dlv_sn_part_rmk1,'')='') tmp;



​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);

​	**END**;

**$function$**

;
```

```sql


-- DROP FUNCTION public.af_pda_wms_sales_outbound_inspection(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_outbound_inspection(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能  ：出货检验扫描（OQC扫描）

 \* 描述  ：销售出库OQC检验合格后，扫描绑定HW的相关信息; HW_SN 包含 HW_CARTON_SN 与 19_CODE

 \* 时间  ：

 \* 开发者：

 */

​	**declare** 

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;

​	

​		_bill_no **text**;

​		_bill_id **text**;

​		_client_no **text**;

​		_sn_no **text**;

​		_part_no **text**;

​	

​		_prod_09_code **text**;

​		_hw_sn **text**;

​		_hw_carton_sn **text**;

​	

​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;	

​		res returntype;

​	**begin**

​		json_datas := **json**(datas);	

​		_user_no := json_datas->>'user_no';

​	

​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'jy_sn_no';

​		_prod_09_code := json_datas->>'jy_09_code';

​		_hw_sn := json_datas->>'hw_sn';

​		_hw_carton_sn := json_datas->>'hw_carton_sn';

​	

​		**select** cr_dlv_h_id,client_no **into** _bill_id,_client_no **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no;

​	

​		--insert into public.a_test_log values(datas, 'pda_wms_sales_outbound_inspection', localtimestamp);

​		--	res := row('true', '销售出库检验扫描完成');

​		--return to_json(res);

​	

​		**select** part_no **into** _part_no **from** wm_sn **where** sn_no=_sn_no;

​		

​		--------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** cr_dlv_h **where** cr_dlv_h_no=_bill_no **and** **coalesce**(cr_dlv_h_rmk6,'')='拣货完成') **then**

​			res := **row**('false','扫描销售出库单没有拣货完成，不能绑定出货信息。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no) **then**

​			res := **row**('false','扫描出货通知单号与产品SN不匹配(不属于此销售出库单的拣货产品)。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** sn_no=_sn_no 

​				**and** **coalesce**(cr_dlv_sn_part_rmk1,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk2,'')='' **and** **coalesce**(cr_dlv_sn_part_rmk3,'')='') **then**

​			res := **row**('false', '扫描产品已经绑定信息，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		-------------------------------------------------------

​		**if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no **and** sn_status='810') **then** 

​			--res := row('false', '此产品不是待OQC检验状态，请核对。');

​			--return to_json(res);

​		**end** **if**;

​		----------------------------------------------------

​		/*if strpos(_sn_no, concat('19',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品19码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;

​	

​		if strpos(_prod_09_code, concat('09',substring(split_part(_part_no,_client_no,1),2)))<>1 then

​			res := row('false', '产品09码与选择出库单明细行(销售订单+产品编码的组合码)不一致。');

​			return to_json(res);

​		end if;*/

​		

​		**if** **strpos**(_hw_sn, _hw_carton_sn)=0 **then**

​			res := **row**('false', '扫描华为二维码与 HW外箱码不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **strpos**(_hw_sn, _sn_no)=0 **then**

​			res := **row**('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');

​			**return** **to_json**(res);

​		**end** **if**;

​		---------------------------------------------------	

​		/*

​		if not exists(select 1 from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and sn_no=_sn_no) then

​			res := row('false','此产品没有销售出库拣货，不能做出货检验。');

​			return to_json(res);

​		end if;

​		

​		------无需生成OQC出货检验单--------

​		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id

​						where a.move_order_id=_bill_id and b.sn_no=_sn_no) then

​			res := row('false', '此产品没有OQC的检验记录，不能做出库扫描');

​			return to_json(res);

​		end if;

​		*/

​		

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk2=_hw_sn) **then**

​			res := **row**('false', '此华为SN已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​	

​		**if** **exists**(**select** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** cr_dlv_sn_part_rmk3=_hw_carton_sn) **then**

​			res := **row**('false', '此华为卡通箱号已经绑定，不能二次绑定。');

​			**return** **to_json**(res);

​		**end** **if**;

​		--------------------------------------------------------------------------------------------



​		**update** public.cr_dlv_sn_part **set** cr_dlv_sn_part_rmk1=_prod_09_code,cr_dlv_sn_part_rmk2=_hw_sn,cr_dlv_sn_part_rmk3=_hw_carton_sn,

​			upd_time=**localtimestamp**

​		**where** sn_no=_sn_no **and** cr_dlv_h_id=_bill_id;	

​	

​		**if** **not** **exists**(**select** **distinct** 1 **from** cr_dlv_sn_part **where** cr_dlv_h_id=_bill_id **and** **coalesce**(cr_dlv_sn_part_rmk1,'')='') **then**

​			**update** public.cr_dlv_h **set** cr_dlv_h_rmk6='OQC绑定信息完成'

​			**where** cr_dlv_h_no=_bill_no;

​		**end** **if**;

​	

​		/*

​		update public.wm_sn set sn_status='820',sn_status_name='OQC检验',upd_time=localtimestamp

​		where sn_no=_sn_no;

​		*/

​		res := **row**('true', '销售出库检验扫描完成');

​		**return** **to_json**(res);



​	**EXCEPTION** **WHEN** **OTHERS** **THEN** 

​		**GET** STACKED **DIAGNOSTICS** 

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);	



​	**END**;

**$function$**

;
```





## 功能逻辑分析

### 一、数据逻辑分析

#### 1. 数据流转路径
```
拣货完成的出库单 → 品质扫描绑定 → 更新cr_dlv_sn_part备注字段 → 完成OQC绑定
                         ↓
                   绑定HW相关信息
                   - 09码(cr_dlv_sn_part_rmk1)
                   - HW_SN(cr_dlv_sn_part_rmk2)  
                   - HW外箱码(cr_dlv_sn_part_rmk3)
```

#### 2. 核心数据处理
- **查询功能**(`af_query_wms_sales_outbound_list_sn`)：查询拣货完成但未绑定品质信息的产品
- **绑定功能**(`af_pda_wms_sales_outbound_inspection`)：扫描并绑定华为相关信息

### 二、业务逻辑分析

#### 1. 业务场景
- **OQC（出货品质检验）扫描绑定**
- 针对华为客户的特殊要求，需要绑定额外的标识信息
- 在拣货完成后、发货前的品质检验环节

#### 2. 关键业务规则

##### 2.1 前置条件验证
- 出库单必须处于"拣货完成"状态（`cr_dlv_h_rmk6='拣货完成'`）
- 产品必须已经完成拣货（存在于`cr_dlv_sn_part`表中）
- 产品未绑定过品质信息（备注字段为空）

##### 2.2 数据一致性验证
```sql
-- HW二维码必须包含外箱码
if strpos(_hw_sn, _hw_carton_sn)=0 then
    res := row('false', '扫描华为二维码与 HW外箱码不一致。');
end if;

-- HW二维码必须包含产品19码
if strpos(_hw_sn, _sn_no)=0 then
    res := row('false', '扫描华为二维码与产品19码（金洋包装条码）不一致。');
end if;
```

##### 2.3 唯一性验证
- 同一HW_SN不能重复绑定
- 同一HW外箱码不能重复绑定

#### 3. 状态流转
- 单据状态：拣货完成 → OQC绑定信息完成（所有产品绑定后）
- 库存状态：原设计有更新为820（OQC检验），但代码中已注释

### 三、代码逻辑分析

#### 1. 查询逻辑分析
```sql
-- af_query_wms_sales_outbound_list_sn
select cb.sn_no, cb.part_qty, cb.part_no 
from cr_dlv_h ca 
left join cr_dlv_sn_part cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id
where ca.cr_dlv_h_no=_bill_no 
  and coalesce(ca.cr_dlv_h_rmk6,'')='拣货完成'
  and coalesce(cb.cr_dlv_sn_part_rmk1,'')=''  -- 未绑定的记录
```

#### 2. 绑定逻辑分析
主要步骤：
1. 验证出库单状态
2. 验证产品是否属于该出库单
3. 验证产品是否已绑定
4. 验证HW信息的一致性和唯一性
5. 更新绑定信息
6. 判断是否全部完成并更新单据状态

#### 3. 潜在问题
- **状态验证被注释**：原有的库存状态验证（810）被注释，可能导致非拣货状态的产品也能绑定
- **质检单关联被注释**：与`qm_si_lot_h`质检单的关联验证被注释，降低了数据完整性要求

### 四、表关联逻辑分析

#### 1. 核心表关系
```mermaid
graph TD
    A[cr_dlv_h 出库单头] -->|cr_dlv_h_id| B[cr_dlv_sn_part 拣货记录]
    C[wm_sn 序列号库存] -->|sn_no| B
    B -->|绑定HW信息| D[品质信息字段]
    E[qm_si_lot_h 质检单头] -.->|已注释| B
    F[qm_si_lot_b_sn 质检明细] -.->|已注释| B
    
    D --> |rmk1| G[09码]
    D --> |rmk2| H[HW_SN]
    D --> |rmk3| I[HW外箱码]
    
    style A fill:#ff9999
    style B fill:#99ff99
    style C fill:#ffcc99
    style E fill:#cccccc
    style F fill:#cccccc
```

#### 2. 关键字段说明
- `cr_dlv_sn_part_rmk1`：存储产品09码
- `cr_dlv_sn_part_rmk2`：存储华为SN（包含多个信息的二维码）
- `cr_dlv_sn_part_rmk3`：存储华为外箱码
- `cr_dlv_sn_part_rmk6`：存储关联的明细ID（从拣货环节继承）

#### 3. 数据完整性保证
- 通过唯一性验证防止重复绑定
- 通过一致性验证确保数据关联正确
- 事务控制确保数据更新的原子性

### 五、优化建议

#### 1. 恢复必要的验证
建议恢复被注释的验证逻辑：
```sql
-- 恢复库存状态验证
if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='810') then 
    res := row('false', '此产品不是待OQC检验状态，请核对。');
    return to_json(res);
end if;
```

#### 2. 增强错误处理
- 添加更详细的错误日志
- 对不同类型的错误提供更明确的提示

#### 3. 性能优化
- 对`cr_dlv_sn_part`的备注字段建立索引
- 批量处理多个产品的绑定

#### 4. 业务增强
- 支持解绑功能，处理扫描错误的情况
- 增加扫描历史记录
- 支持不同客户的不同绑定要求配置化