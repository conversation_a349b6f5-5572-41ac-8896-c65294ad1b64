```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_loading(varchar, varchar, varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_loading(
    number_plate_no character varying,
    bill_no character varying, 
    sn_no character varying
)
RETURNS character varying
LANGUAGE plpgsql
AS $function$
/*
 * 功能：销售出库装车
 * 描述：品质扫描完成后的装车环节，更新库存状态为830，单据状态为出库完成
 * 参数：number_plate_no - 车牌号
 *      bill_no - 出库单号
 *      sn_no - 产品条码
 * 时间：
 * 开发者：
 */
declare 
    json_datas json;
    _user_id text;
    _user_no text;
    _user_name text;
    _host text;

    _bill_id text;
    _client_no text;
    _part_no text;
    _part_qty numeric;
    _sn_type text;
    _invp_area text;

    _err_msg_text text;
    _err_pg_detail text;
    _err_msg text;	
    res returntype;
begin
    -- 参数验证
    if coalesce(number_plate_no, '') = '' then
        res := row('false', '车牌号不能为空');
        return to_json(res);
    end if;
    
    if coalesce(bill_no, '') = '' then
        res := row('false', '出库单号不能为空');
        return to_json(res);
    end if;
    
    if coalesce(sn_no, '') = '' then
        res := row('false', '产品条码不能为空');
        return to_json(res);
    end if;

    -- 获取用户信息（这里需要根据实际情况获取当前用户）
    -- select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
    _user_id := 'SYSTEM';
    _user_no := 'SYSTEM';
    _user_name := '系统用户';

    -- 验证出库单状态
    if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=bill_no and coalesce(cr_dlv_h_rmk6,'')='OQC绑定信息完成') then
        res := row('false', format('出库单【%s】不存在或状态不正确，必须是OQC绑定信息完成状态才能装车', bill_no));
        return to_json(res);
    end if;

    -- 获取出库单信息
    select cr_dlv_h_id, client_no into _bill_id, _client_no 
    from cr_dlv_h where cr_dlv_h_no = bill_no;

    -- 获取产品信息
    select part_no, part_qty, sn_type, invp_area_no 
    into _part_no, _part_qty, _sn_type, _invp_area  
    from wm_sn where sn_no = sn_no;

    if _part_no is null then
        res := row('false', format('产品条码【%s】在库存中不存在', sn_no));
        return to_json(res);
    end if;

    -- 验证产品是否属于该出库单
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id and sn_no = sn_no) then
        res := row('false', format('产品条码【%s】不属于出库单【%s】', sn_no, bill_no));
        return to_json(res);
    end if;

    -- 验证产品是否已完成品质扫描绑定
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id and sn_no = sn_no 
                  and coalesce(cr_dlv_sn_part_rmk1,'') != '' 
                  and coalesce(cr_dlv_sn_part_rmk2,'') != '' 
                  and coalesce(cr_dlv_sn_part_rmk3,'') != '') then
        res := row('false', format('产品条码【%s】未完成品质扫描绑定，不能装车', sn_no));
        return to_json(res);
    end if;

    -- 验证库存状态
    if not exists(select 1 from wm_sn where sn_no = sn_no and sn_status = '820') then
        res := row('false', format('产品条码【%s】库存状态不正确，必须是820（OQC检验）状态才能装车', sn_no));
        return to_json(res);
    end if;

    -- 验证是否已装车
    if exists(select 1 from cr_dlv_sn_part 
              where cr_dlv_h_id = _bill_id and sn_no = sn_no 
              and coalesce(cr_dlv_sn_part_rmk4,'') != '') then
        res := row('false', format('产品条码【%s】已装车，车牌号：%s', sn_no, 
                   (select cr_dlv_sn_part_rmk4 from cr_dlv_sn_part 
                    where cr_dlv_h_id = _bill_id and sn_no = sn_no limit 1)));
        return to_json(res);
    end if;

    -- 更新装车信息
    update public.cr_dlv_sn_part 
    set cr_dlv_sn_part_rmk4 = number_plate_no,
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where cr_dlv_h_id = _bill_id and sn_no = sn_no;

    -- 更新库存状态为830（装车）
    update public.wm_sn 
    set sn_status = '830',
        sn_status_name = '装车',
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where sn_no = sn_no;

    -- 检查是否所有产品都已装车完成
    if not exists(select 1 from cr_dlv_sn_part 
                  where cr_dlv_h_id = _bill_id 
                  and coalesce(cr_dlv_sn_part_rmk4,'') = '') then
        -- 所有产品都已装车，更新出库单状态为出库完成
        update public.cr_dlv_h 
        set cr_dlv_h_rmk6 = '出库完成',
            upd_time = localtimestamp,
            upd_user = _user_id,
            upd_user_no = _user_no,
            upd_user_name = _user_name
        where cr_dlv_h_id = _bill_id;
    end if;

    res := row('true', format('产品【%s】装车成功，车牌号：%s', sn_no, number_plate_no));
    return to_json(res);

EXCEPTION WHEN OTHERS THEN 
    GET STACKED DIAGNOSTICS 
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;

    _err_msg := format('错误信息:%s,详情:%s', _err_msg_text, _err_pg_detail);
    res := row('false', _err_msg);
    return to_json(res);	

END;
$function$;
```

## 功能逻辑深度分析

### 一、数据逻辑深度分析

#### 1.1 数据流转的完整生命周期
```mermaid
graph TD
    A[品质扫描完成] --> B[cr_dlv_h_rmk6='OQC绑定信息完成']
    B --> C[扫描车牌号和产品条码]
    C --> D[验证装车条件]
    D --> E[更新装车信息]
    E --> F[更新库存状态830]
    F --> G[检查全部装车完成]
    G --> H[更新单据状态'出库完成']
    
    subgraph "数据状态变化"
        I[wm_sn.sn_status: 820] --> J[wm_sn.sn_status: 830装车]
        K[cr_dlv_sn_part_rmk4: 空] --> L[cr_dlv_sn_part_rmk4: 车牌号]
        M[cr_dlv_h_rmk6: OQC绑定信息完成] --> N[cr_dlv_h_rmk6: 出库完成]
    end
```

#### 1.2 数据验证的层次结构
**第一层：参数基础验证**
- 车牌号非空验证：`coalesce(number_plate_no, '') = ''`
- 出库单号非空验证：`coalesce(bill_no, '') = ''`
- 产品条码非空验证：`coalesce(sn_no, '') = ''`

**第二层：业务状态验证**
- 出库单状态验证：`cr_dlv_h_rmk6='OQC绑定信息完成'`
- 库存状态验证：`sn_status = '820'`（OQC检验状态）
- 品质绑定验证：检查rmk1、rmk2、rmk3字段非空

**第三层：业务关联验证**
- 产品归属验证：产品必须属于该出库单
- 重复装车验证：检查是否已装车（rmk4字段）
- 数据完整性验证：确保产品和出库单数据完整

#### 1.3 关键数据字段的作用
**状态控制字段**：
- `wm_sn.sn_status`：库存状态，从820（OQC检验）→830（装车）
- `cr_dlv_h_rmk6`：单据状态，从'OQC绑定信息完成'→'出库完成'
- `cr_dlv_sn_part_rmk4`：装车标识，存储车牌号

**追溯信息字段**：
- `number_plate_no`：车牌号，记录在rmk4字段中
- `upd_time`：更新时间，记录装车时间
- `upd_user`：操作用户，记录装车操作人

### 二、业务逻辑深度分析

#### 2.1 业务场景与流程定位
**核心业务场景**：
- **销售出库的最后环节**：品质检验完成后的装车作业
- **物流交接节点**：从仓库管理转向运输管理
- **状态完结标识**：标志着WMS出库流程的完成
- **追溯信息补全**：记录最终的物流承运信息

**在整体流程中的位置**：
```
拣货 → 品质扫描 → 装车 → 出库完成 → SAP接口推送
```

#### 2.2 装车业务的关键规则
**前置条件验证**：
1. **出库单状态**：必须是'OQC绑定信息完成'状态
2. **库存状态**：产品必须是820（OQC检验）状态
3. **品质绑定**：必须完成华为相关信息绑定（rmk1、rmk2、rmk3非空）
4. **产品归属**：产品必须属于当前出库单

**装车控制规则**：
1. **防重装车**：同一产品不能重复装车
2. **车牌记录**：准确记录承运车辆信息
3. **状态同步**：库存状态与装车状态同步更新
4. **完成判断**：所有产品装车后更新单据状态

#### 2.3 状态流转的业务逻辑
**库存状态流转**：
- 820（OQC检验）→ 830（装车）
- 标志着产品从质检环节进入物流环节

**单据状态流转**：
- 'OQC绑定信息完成' → '出库完成'
- 标志着WMS出库流程的完整结束

**渐进式完成机制**：
- 支持逐个产品装车
- 全部装车完成后自动更新单据状态
- 确保业务流程的连续性和完整性

#### 2.4 与前后环节的业务衔接
**与品质扫描的衔接**：
- 依赖品质扫描完成的状态和数据
- 验证华为相关信息的绑定完整性
- 确保质量管控的连续性

**与SAP接口的衔接**：
- 为SAP接口推送提供完整的出库数据
- 确保状态标识的准确性
- 支持完整的业务闭环

### 三、代码逻辑深度分析

#### 3.1 函数参数设计分析
**参数设计特点**：
```sql
af_pda_wms_sales_outbound_loading(
    number_plate_no character varying,  -- 车牌号：物流承运信息
    bill_no character varying,          -- 出库单号：业务单据标识
    sn_no character varying            -- 产品条码：具体装车产品
)
```

**设计优势**：
- **参数简洁**：只包含装车必需的核心信息
- **业务明确**：每个参数都有明确的业务含义
- **操作便捷**：适合PDA扫描操作的参数结构

#### 3.2 验证逻辑的层次化设计
**参数验证层**：
```sql
-- 基础参数非空验证
if coalesce(number_plate_no, '') = '' then
    res := row('false', '车牌号不能为空');
    return to_json(res);
end if;
```

**业务状态验证层**：
```sql
-- 出库单状态验证
if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=bill_no and coalesce(cr_dlv_h_rmk6,'')='OQC绑定信息完成') then
    res := row('false', format('出库单【%s】不存在或状态不正确，必须是OQC绑定信息完成状态才能装车', bill_no));
    return to_json(res);
end if;
```

**业务关联验证层**：
```sql
-- 产品归属验证
if not exists(select 1 from cr_dlv_sn_part
              where cr_dlv_h_id = _bill_id and sn_no = sn_no) then
    res := row('false', format('产品条码【%s】不属于出库单【%s】', sn_no, bill_no));
    return to_json(res);
end if;
```

#### 3.3 状态更新的原子性设计
**装车信息更新**：
```sql
-- 更新装车信息
update public.cr_dlv_sn_part
set cr_dlv_sn_part_rmk4 = number_plate_no,
    upd_time = localtimestamp,
    upd_user = _user_id,
    upd_user_no = _user_no,
    upd_user_name = _user_name
where cr_dlv_h_id = _bill_id and sn_no = sn_no;
```

**库存状态更新**：
```sql
-- 更新库存状态为830（装车）
update public.wm_sn
set sn_status = '830',
    sn_status_name = '装车',
    upd_time = localtimestamp,
    upd_user = _user_id,
    upd_user_no = _user_no,
    upd_user_name = _user_name
where sn_no = sn_no;
```

**条件性单据状态更新**：
```sql
-- 检查是否所有产品都已装车完成
if not exists(select 1 from cr_dlv_sn_part
              where cr_dlv_h_id = _bill_id
              and coalesce(cr_dlv_sn_part_rmk4,'') = '') then
    -- 所有产品都已装车，更新出库单状态为出库完成
    update public.cr_dlv_h
    set cr_dlv_h_rmk6 = '出库完成',
        upd_time = localtimestamp,
        upd_user = _user_id,
        upd_user_no = _user_no,
        upd_user_name = _user_name
    where cr_dlv_h_id = _bill_id;
end if;
```

#### 3.4 错误处理机制
**统一异常处理**：
```sql
EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;

    _err_msg := format('错误信息:%s,详情:%s', _err_msg_text, _err_pg_detail);
    res := row('false', _err_msg);
    return to_json(res);
```

**友好错误提示**：
- 使用`format`函数构建详细的错误信息
- 包含具体的业务参数，便于问题定位
- 统一的JSON返回格式

### 四、表关联逻辑深度分析

#### 4.1 核心表结构关系图
```mermaid
erDiagram
    cr_dlv_h ||--o{ cr_dlv_sn_part : "1对多"
    wm_sn ||--o{ cr_dlv_sn_part : "1对多"
    ss_user ||--o{ cr_dlv_sn_part : "操作审计"

    cr_dlv_h {
        varchar cr_dlv_h_id PK "出库单ID"
        varchar cr_dlv_h_no UK "出库单号"
        varchar cr_dlv_h_rmk6 "单据状态"
        varchar client_no "客户编号"
    }

    cr_dlv_sn_part {
        varchar cr_dlv_sn_part_id PK "拣货记录ID"
        varchar cr_dlv_h_id FK "出库单ID"
        varchar sn_no FK "序列号"
        varchar cr_dlv_sn_part_rmk1 "09码"
        varchar cr_dlv_sn_part_rmk2 "HW_SN"
        varchar cr_dlv_sn_part_rmk3 "HW外箱码"
        varchar cr_dlv_sn_part_rmk4 "车牌号"
    }

    wm_sn {
        varchar sn_no PK "序列号"
        varchar part_no "物料编码"
        varchar sn_status "库存状态"
        varchar sn_status_name "状态名称"
        varchar invp_area_no "仓库区域"
    }
```

#### 4.2 关键关联查询分析
**出库单状态验证查询**：
```sql
select 1 from cr_dlv_h
where cr_dlv_h_no=bill_no and coalesce(cr_dlv_h_rmk6,'')='OQC绑定信息完成'
```

**产品归属验证查询**：
```sql
select 1 from cr_dlv_sn_part
where cr_dlv_h_id = _bill_id and sn_no = sn_no
```

**品质绑定完整性验证查询**：
```sql
select 1 from cr_dlv_sn_part
where cr_dlv_h_id = _bill_id and sn_no = sn_no
and coalesce(cr_dlv_sn_part_rmk1,'') != ''
and coalesce(cr_dlv_sn_part_rmk2,'') != ''
and coalesce(cr_dlv_sn_part_rmk3,'') != ''
```

**装车完成度检查查询**：
```sql
select 1 from cr_dlv_sn_part
where cr_dlv_h_id = _bill_id
and coalesce(cr_dlv_sn_part_rmk4,'') = ''
```

#### 4.3 数据完整性保证机制
**事务原子性**：
- 整个装车过程在单一事务中执行
- 任何验证失败都会阻止后续操作
- 确保数据状态的一致性

**状态同步机制**：
- 库存状态与装车记录同步更新
- 单据状态与装车进度同步
- 审计信息完整记录

**业务完整性验证**：
- 多层次的业务规则验证
- 前置条件的严格检查
- 数据关联的完整性保证

### 五、实际应用场景分析

#### 5.1 典型装车作业流程
```
1. 品质人员完成OQC绑定 → cr_dlv_h_rmk6='OQC绑定信息完成'
2. 装车人员扫描车牌号 → 记录承运车辆信息
3. 逐个扫描产品条码 → 系统验证并记录装车
4. 更新库存状态830 → 标识产品已装车
5. 全部装车完成 → cr_dlv_h_rmk6='出库完成'
6. 触发SAP接口推送 → 完成业务闭环
```

#### 5.2 业务价值分析
**物流管控价值**：
- 准确记录承运车辆信息
- 建立产品到车辆的追溯关系
- 支持物流过程的精确管控

**状态管理价值**：
- 明确标识出库流程的完成
- 为后续接口推送提供准确状态
- 确保业务流程的完整性

**追溯管理价值**：
- 完整的装车操作记录
- 详细的时间和操作人信息
- 支持问题追溯和责任定位

#### 5.3 系统设计的优势与不足

**优势分析**：
1. **业务流程完整**：覆盖装车环节的完整业务逻辑
2. **验证机制严密**：多层次的业务规则验证
3. **状态管理精确**：准确的状态流转和同步
4. **追溯信息完整**：详细的操作审计信息
5. **错误处理友好**：清晰的错误提示和异常处理

**潜在改进空间**：
1. **用户信息获取**：当前使用固定的系统用户，需要集成实际用户信息
2. **并发控制**：缺少对同一产品并发装车的控制
3. **批量操作支持**：当前只支持单个产品装车，可考虑批量装车
4. **装车撤销功能**：缺少装车错误的撤销机制

### 六、优化建议

#### 6.1 功能增强建议
1. **用户身份集成**：
   ```sql
   -- 集成实际用户信息
   CREATE OR REPLACE FUNCTION af_pda_wms_sales_outbound_loading(
       number_plate_no character varying,
       bill_no character varying,
       sn_no character varying,
       user_no character varying  -- 增加用户参数
   )
   ```

2. **批量装车支持**：
   ```sql
   -- 支持批量装车
   CREATE OR REPLACE FUNCTION af_pda_wms_sales_outbound_loading_batch(
       number_plate_no character varying,
       bill_no character varying,
       sn_nos json  -- JSON数组支持多个条码
   )
   ```

3. **装车撤销功能**：
   ```sql
   -- 装车撤销函数
   CREATE OR REPLACE FUNCTION af_pda_wms_sales_outbound_loading_cancel(
       bill_no character varying,
       sn_no character varying
   )
   ```

#### 6.2 性能优化建议
1. **索引优化**：
   ```sql
   CREATE INDEX idx_cr_dlv_h_no_rmk6 ON cr_dlv_h(cr_dlv_h_no, cr_dlv_h_rmk6);
   CREATE INDEX idx_cr_dlv_sn_part_h_sn ON cr_dlv_sn_part(cr_dlv_h_id, sn_no);
   CREATE INDEX idx_wm_sn_no_status ON wm_sn(sn_no, sn_status);
   ```

2. **并发控制**：
   ```sql
   -- 增加行级锁防止并发问题
   SELECT 1 FROM cr_dlv_sn_part
   WHERE cr_dlv_h_id = _bill_id AND sn_no = sn_no
   FOR UPDATE;
   ```

### 七、总结

PDA销售出库装车存储过程是WMS出库流程的关键收尾环节，通过严密的业务验证和精确的状态管理，确保了从品质检验到物流交接的平滑过渡。该存储过程体现了制造业WMS系统对精细化管理和完整追溯的要求，为企业的物流管控和质量追溯提供了重要支撑。

**核心价值**：
- 完成WMS出库流程的最后环节
- 建立完整的物流追溯链条
- 确保业务状态的准确流转

**技术特色**：
- 多层次的业务验证机制
- 原子性的状态更新操作
- 完整的错误处理和审计

**应用价值**：
- 支持精确的物流管控
- 提供完整的追溯信息
- 确保业务流程的完整性

