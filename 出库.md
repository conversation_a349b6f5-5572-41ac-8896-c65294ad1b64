# 销售出库系统完整分析

## 一、系统概述

### 1.1 系统架构
```mermaid
graph TD
    subgraph "系统入口"
        A[SAP出库申请单同步]
    end
    
    subgraph "出库单管理"
        B[销售出库_SAP申请_列表<br/>AX_销售出库_SAP申请_列表.md]
    end
    
    subgraph "有序列号出库流程"
        C[PDA_销售出库拣货<br/>af_pda_wms_sales_outbound_picking]
        D[PDA_销售出库扫描品质<br/>af_pda_wms_sales_outbound_inspection]
    end
    
    subgraph "无序列号出库流程"
        E[PDA_销售出库_无序列号<br/>af_pda_wms_sales_outbound_no_sn]
    end
    
    subgraph "特殊出库流程"
        F[PDA_销售出库发货通知单+条码<br/>af_wm_sales_out_notice_order]
    end
    
    subgraph "系统出口"
        G[接口_MES-SAP销售发货单]
        H[销售出库完成]
    end
    
    A --> B
    B -->|有序列号| C
    C --> D
    D --> G
    B -->|无序列号| E
    E --> G
    B -.->|特殊流程| F
    F -.->|数据不完整| X[无法对接SAP]
    G --> H
    
    style A fill:#ff9999
    style B fill:#99ccff
    style G fill:#99ff99
    style H fill:#ffff99
    style F fill:#cccccc
    style X fill:#ff6666
```

![1751429182250](D:\金洋\成品出入库记录\出库\assets\1751429182250.png)

### 1.2 核心数据表

```mermaid
graph TD
    subgraph "出库单据"
        A[cr_dlv_h 出库单头]
        B[cr_dlv_b 出库单明细]
        C[cr_dlv_sn_part 拣货/出库记录]
    end
    
    subgraph "库存数据"
        D[wm_sn 序列号库存]
        E[szjy_wm_inventory 批次库存]
    end
    
    subgraph "基础数据"
        F[pd_part 物料主数据]
        G[ss_user 用户信息]
        H[ss_cdvl 状态字典]
    end
    
    subgraph "其他相关"
        I[wm_io 出入库记录]
        J[me_mtr_use_sn_part 发料记录]
        K[qm_si_lot_h/b_sn 质检记录]
    end
    
    A -->|1:N| B
    A -->|1:N| C
    B -->|cr_dlv_b_id=cr_dlv_sn_part_rmk6| C
    D -->|序列号| C
    E -->|批次| C
    
    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#99ff99
    style D fill:#ffcc99
    style E fill:#ffcc99
```

![1751429118491](D:\金洋\成品出入库记录\出库\assets\1751429118491.png)

## 二、业务流程详解

### 2.1 有序列号出库流程（标准流程）

#### 流程说明
适用于高价值、需要精确追踪的产品，通过逐个扫描序列号完成出库。

#### 流程步骤

##### 步骤1：销售出库拣货（PDA_销售出库拣货.md）
- **功能**：`af_pda_wms_sales_outbound_picking`
- **业务规则**：
  - 验证出库单状态（必须为待拣货）
  - 验证库存状态（必须为在库800）
  - FIFO验证（启用先进先出的产品）
  - 数量控制（不超过计划数量）
  - 支持同一物料多行明细智能分配
- **状态变化**：
  - 库存状态：800（在库）→ 810（拣货）→ 820（OQC检验）→ 830（出库）
  - 明细状态：空 → "拣货完成"
  - 单头状态：空 → "拣货完成"（全部完成时）
- **关键问题**：箱数更新只更新第一行，导致SAP接口数据错误

##### 步骤2：销售出库品质扫描（PDA_销售出库扫描(品质).md）
- **功能**：`af_pda_wms_sales_outbound_inspection`
- **业务场景**：华为客户特殊要求，绑定额外标识
- **绑定信息**：
  - `cr_dlv_sn_part_rmk1`：09码
  - `cr_dlv_sn_part_rmk2`：HW_SN
  - `cr_dlv_sn_part_rmk3`：HW外箱码
- **状态变化**：
  - 单据状态：拣货完成 → OQC绑定信息完成
- **注意事项**：质检验证已被注释，降低了数据完整性要求

##### 步骤3：推送SAP（接口_MES-SAP销售发货单.md）
- **触发条件**：`cr_dlv_h_rmk6='发货完成'` 且 `sap_bill_no`为空
- **数据处理**：
  - 按明细行、批次、库位汇总数量
  - 生成批次JSON数组
  - 根据出库类型（订单/非订单）构建不同格式
- **关键字段**：
  - `cr_dlv_sn_part_rmk6`：关联明细ID
  - `cr_dlv_b_rmk5`：箱数（U_PLqty）
- **成功处理**：更新`sap_bill_no`防止重复推送

### 2.2 无序列号出库流程（简化流程）

#### 流程说明
适用于批次管理但不需要序列号追踪的产品，一次性完成整单出库。

#### 流程特点（PDA_销售出库_无序列号_.md）
- **功能**：`af_pda_wms_sales_outbound_no_sn`
- **业务规则**：
  - 验证库存充足性（总量和分仓库）
  - 按批次FIFO自动分配
  - 双重循环处理（明细×批次）
- **数据处理**：
  - 使用`szjy_wm_inventory`批次库存表
  - 在`cr_dlv_sn_part`记录批次信息（sn_no为空）
  - 直接更新状态为"发货完成"
- **优势**：流程简单，无需逐个扫描
- **问题**：缺少并发控制，可能导致库存扣减错误

### 2.3 特殊出库流程（未完善）

#### 流程说明（PDA_销售出库（发货通知单+条码）.md）
- **功能组**：
  - `af_sales_notice_head`：验证通知单
  - `af_sales_notice_bf`：验证条码
  - `af_wm_sales_out_notice_order`：执行出库
- **验证最严格**：
  - 上架状态、退货记录、发料记录
  - 品号匹配、防重复扫描
- **关键问题**：
  - 只清空库存数量，不更新状态
  - 不更新出库单状态和数量
  - 无法与SAP接口对接
- **可能用途**：客户赔偿、报废品等特殊场景

## 三、关键数据结构

### 3.1 状态流转
| 环节 | cr_dlv_h.cr_dlv_h_rmk6 | wm_sn.sn_status | 说明 |
|------|------------------------|-----------------|------|
| 初始 | 空 | 800（在库） | 待拣货状态 |
| 拣货完成 | 拣货完成 | 810（拣货） | 有序列号 |
| 品质完成 | OQC绑定信息完成 | 810 | 华为客户 |
| 发货完成 | 发货完成 | - | 可推送SAP |

### 3.2 关键字段用途
| 字段 | 表 | 用途 |
|------|-----|------|
| cr_dlv_h_rmk6 | cr_dlv_h | 出库单状态标识 |
| cr_dlv_b_rmk5 | cr_dlv_b | 箱数（ctn_num） |
| cr_dlv_b_rmk6 | cr_dlv_b | 明细状态 |
| cr_dlv_sn_part_rmk1 | cr_dlv_sn_part | 09码（品质） |
| cr_dlv_sn_part_rmk2 | cr_dlv_sn_part | HW_SN（品质） |
| cr_dlv_sn_part_rmk3 | cr_dlv_sn_part | HW外箱码（品质） |
| cr_dlv_sn_part_rmk6 | cr_dlv_sn_part | 关联明细ID |

## 四、系统问题汇总

### 4.1 严重问题
1. **箱数更新BUG**（PDA_销售出库拣货）
   - 同一物料多行只更新第一行箱数
   - 导致SAP接口数据分组错误
   - 影响范围：所有有序列号出库

2. **特殊流程数据不完整**（通知单+条码）
   - 不更新必要状态，无法对接SAP
   - 库存处理方式异常（只清零）
   - 影响范围：特殊场景出库

### 4.2 中等问题
1. **并发控制缺失**（无序列号出库）
   - 无显式锁机制
   - 可能导致库存扣减错误

2. **FIFO逻辑不严谨**（拣货）
   - 使用字符串比较日期
   - 性能和准确性问题

3. **验证逻辑被削弱**（品质扫描）
   - 质检关联被注释
   - 库存状态验证被注释

### 4.3 优化建议
1. **修复箱数更新逻辑**
   ```sql
   -- 更新所有相关行而非仅第一行
   update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
   from (select part_no, count(distinct sn_no) as ctn_num ...) tt
   where cr_dlv_b.cr_dlv_h_id=_bill_id 
     and cr_dlv_b.part_no=tt.part_no;
   ```

2. **增加并发控制**
   ```sql
   SELECT * FROM szjy_wm_inventory 
   WHERE ... FOR UPDATE;
   ```

3. **完善特殊流程**
   - 增加状态更新逻辑
   - 规范库存处理方式
   - 明确业务定位

## 五、业务场景对比

| 特性 | 有序列号出库 | 无序列号出库 | 特殊出库 |
|------|------------|------------|----------|
| 扫描方式 | 逐个扫描 | 扫描单号 | 通知单+条码 |
| 流程复杂度 | 高（多环节） | 低 | 中 |
| 库存管理 | 序列号级 | 批次级 | 序列号级 |
| 状态管理 | 完整 | 完整 | 缺失 |
| SAP对接 | 支持 | 支持 | 不支持 |
| 适用场景 | 高价值产品 | 批量产品 | 特殊需求 |
| FIFO控制 | 严格 | 自动 | 无 |
| 验证严格度 | 中 | 低 | 最高 |

## 六、系统优化路线图

### 6.1 紧急修复（1-2周）
1. 修复有序列号出库的箱数更新BUG
2. 增加无序列号出库的并发控制
3. 明确特殊出库流程的使用限制

### 6.2 短期优化（1个月）
1. 优化FIFO逻辑，使用日期类型
2. 恢复必要的验证逻辑
3. 增加详细的错误日志
4. 性能优化（索引、批量处理）

### 6.3 长期改进（3个月）
1. 统一状态管理机制
2. 增加可配置的验证规则
3. 支持部分出库、撤销等高级功能
4. 建立完整的出库效率分析体系

## 七、总结

该销售出库系统设计灵活，支持多种出库模式，能够满足不同产品特性的需求。但在实施过程中存在一些技术债务，特别是箱数更新BUG和特殊流程的不完整性，需要优先解决。系统的核心价值在于其模块化设计和状态流转机制，通过合理的优化可以成为一个高效可靠的出库管理系统。 