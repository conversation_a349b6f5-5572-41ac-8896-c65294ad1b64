单据编号校验：af_query_wms_sales_outbound_list

```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询明细，并返回销售订单+物料编码的组合码

 \* 描述：

 \* 时间:

 \* 开发者：

 */

​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**begin**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';



​		**insert** **into** public.a_test_log **values**(datas,'query_wms_sales_outbound_list',**localtimestamp**);

​	

​		/*select array_agg(row_to_json(tmp)) into tmp_json 

​		from (select cb.part_no,cb.cr_dlv_qty_plan,cb.so_h_no

​			from cr_dlv_h ca

​			left join cr_dlv_b cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			where ca.cr_dlv_h_no=_bill_no and cb.cr_dlv_qty_plan>coalesce(cb.cr_dlv_qty,0)

​			order by cb.part_no,cb.so_h_no

​			) tmp;*/

​			

​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** t1.part_no,t1.part_qty_plan::**int**,**coalesce**(t2.part_qty_real,0)::**int** **as** part_qty_real,t1.cr_dlv_h_no

​			**from** (**select** ca.cr_dlv_h_no,cb.part_no,**sum**(cb.cr_dlv_qty_plan) **as** part_qty_plan 

​				**from** cr_dlv_h ca

​				**left** **join** cr_dlv_b cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​				**where** ca.cr_dlv_h_no=_bill_no

​				**group** **by** ca.cr_dlv_h_no,cb.part_no) t1

​			**left** **join** (**select** ca2.cr_dlv_h_no,cb2.part_no,**sum**(cb2.part_qty) **as** part_qty_real

​					**from** cr_dlv_h ca2

​					**left** **join** cr_dlv_sn_part cb2 **on** cb2.cr_dlv_h_id=ca2.cr_dlv_h_id

​					**where** ca2.cr_dlv_h_no=_bill_no

​					**group** **by** ca2.cr_dlv_h_no,cb2.part_no) t2 **on** t2.cr_dlv_h_no=t1.cr_dlv_h_no **and** t2.part_no=t1.part_no

​			**where** t1.part_qty_plan>**coalesce**(t2.part_qty_real,0)

​			**order** **by** t1.part_no

​			) tmp;

​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);	

​	

​	**END**;

**$function$**

;


```



销售出库拣货：

```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库拣货
 * 描述   ：
 * 时间   ：
 * 开发者 ：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;
	
		_sn_type text;
		_produce_date date;
		_produce_date_min date;
	
		sub_datas record;
		_prod_lot text;
		_b_id text;
		_invp_area text;
		_client_no text;
		_client_part_no text;
		_prod_cycle text;
		_prod_cycle_min text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	begin
		json_datas := json(datas);	
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		--_part_no := json_datas->>'part_no';	
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		select part_no,part_qty,sn_type,inventory_lot,produce_date::date,invp_area_no into _part_no,_part_qty,_sn_type,_prod_lot,_produce_date,_invp_area  
		from wm_sn where sn_no=_sn_no;
	
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			_err_msg := format('扫描销售出库单【%s】，不是待拣货状态。',_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		select cr_dlv_h_id,client_no into _bill_id,_client_no from cr_dlv_h where cr_dlv_h_no=_bill_no;

		/*
		if _client_no='3031' or _client_no='3039' then
			_client_part_no := substring(split_part(_sn_no,'/',1),3);
			if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
				_part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
			end if;
		end if;
		*/

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】仓库【%s】）不属于此销售出库单【%s】限定的产品与仓库，不能拣货。',_part_no,_sn_no,_invp_area,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);		
		end if;
	
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='800') then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】）不是在库状态，不能拣货。',_part_no,_sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		----------------------------------------------------------------------------------------
		if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
			select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
			--if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and produce_date::date<_produce_date and sn_status='800') then
			if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
				--select min(produce_date)::date into _produce_date_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
				select min(substring(split_part(_sn_no,'/021748',2),1,4)) into _prod_cycle_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';				
				_err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',_part_no,_sn_no,_prod_cycle,_prod_cycle_min);
				res := row('false', _err_msg);
				return to_json(res);
			end if;
		end if;
		----------------------------------------------------------------------------------------
	
		if exists(select 1 from cr_dlv_sn_part where sn_no=_sn_no) then
			res := row('false', '扫描产品条码已经拣货，不能二次拣货。');
			return to_json(res);
		end if;
	
		select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
			_err_msg := format('产品（物料编码【%s】）拣货数量(已拣货数量【%s】+现拣货数量【%s】)大于销售出库单【%s】需要求数量【%s】，不能拣货。',_part_no,coalesce(_part_qty_real,0),_part_qty::int,_bill_no,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		---------------------------------------------------------------------------------------
		if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area having count(*)>1) then
			for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) loop
				if 	sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty >0 and _part_qty>0 then	
					if (sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty)>=_part_qty then
						update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := 0;
					else
						update public.cr_dlv_b set cr_dlv_qty=sub_datas.cr_dlv_qty_plan
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := _part_qty+sub_datas.cr_dlv_qty-sub_datas.cr_dlv_qty_plan;
					end if;
				end if;

			end loop;
		else
			select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
			insert into public.cr_dlv_sn_part
			(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', _b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from pd_part
			where part_no=_part_no;

			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
			where cr_dlv_b_id=_b_id;
		end if;
		---------------------------------------------------------------------------------------

		update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;
		
		---------------------------------------------------------------------------------------

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
			update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where cr_dlv_h_id=_bill_id; 

			update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
			from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
				from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
				left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
				where tmp1.rn=1) tt
			where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
		end if;
	
		update public.wm_sn set sn_status='810',sn_status_name='拣货',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;
		---------------------------------------------------------------------------------------
		res := row('true', '销售出库拣货完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;


```


## 功能逻辑深度分析

### 一、数据逻辑深度分析

#### 1.1 数据流转的完整生命周期
```mermaid
graph TD
    A[扫描条码sn_no] --> B[查询wm_sn库存信息]
    B --> C[验证出库单状态]
    C --> D[验证产品归属性]
    D --> E[FIFO先进先出验证]
    E --> F[数量控制验证]
    F --> G[创建拣货记录]
    G --> H[更新明细数量]
    H --> I[更新库存状态]
    I --> J[计算箱数]
    J --> K[更新单据状态]

    subgraph "数据状态变化"
        L[wm_sn.sn_status: 800在库] --> M[wm_sn.sn_status: 810拣货]
        N[cr_dlv_h_rmk6: 空] --> O[cr_dlv_h_rmk6: 拣货完成]
        P[cr_dlv_b_rmk6: 空] --> Q[cr_dlv_b_rmk6: 拣货完成]
    end
```

#### 1.2 数据验证的层次结构
**第一层：基础数据验证**
- 出库单状态验证：`cr_dlv_h_rmk6=''`（待拣货状态）
- 库存状态验证：`sn_status='800'`（在库状态）
- 重复拣货验证：检查`cr_dlv_sn_part`中是否已存在该条码

**第二层：业务规则验证**
- 产品归属验证：产品必须属于当前出库单的明细
- 仓库匹配验证：产品仓库必须与出库单明细的仓库一致
- 数量控制验证：拣货数量不能超过计划数量

**第三层：高级业务逻辑验证**
- FIFO先进先出验证：检查是否有更早批次的产品
- 客户特殊规则验证：针对特定客户的产品编码处理

#### 1.3 数据一致性保证机制
```sql
-- 数量一致性验证
select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
    -- 阻止超量拣货
end if;
```

#### 1.4 核心数据处理流程详解
1. **条码解析**：从条码中提取产品信息、生产周期等关键数据
2. **库存查询**：获取产品的完整库存信息（数量、批次、仓库等）
3. **出库单匹配**：验证产品是否属于当前出库单的计划范围
4. **FIFO检查**：基于生产周期确保先进先出原则
5. **智能分配**：处理同一物料多行明细的数量分配
6. **状态同步**：确保库存状态与出库单状态的一致性
7. **箱数统计**：计算每个物料的总箱数用于后续流程

### 二、业务逻辑深度分析

#### 2.1 业务场景与应用范围
**核心业务场景**：
- **有序列号管控产品的销售出库拣货**：针对需要精确追溯的产品
- **移动端扫描作业**：支持PDA设备的实时扫描拣货
- **多仓库协同作业**：支持不同仓库的产品同时拣货
- **复杂订单处理**：支持一个订单包含多个物料、多个仓库的情况

#### 2.2 FIFO先进先出业务逻辑深度解析
```sql
-- FIFO验证的核心逻辑
if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
    select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
    if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type
              and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
        -- 阻止拣货，要求先拣更早批次
    end if;
end if;
```

**FIFO业务规则特点**：
- **条码解析**：从条码中的`/021748`标识后提取4位生产周期
- **批次比较**：使用字符串比较确定批次先后顺序
- **类型匹配**：同一产品的同一类型（sn_type）之间进行FIFO控制
- **状态过滤**：只考虑在库状态的产品进行比较

#### 2.3 多行明细智能分配业务逻辑
**业务背景**：同一物料可能在出库单中有多行明细（不同销售订单、不同交期等）

**分配策略**：
1. **顺序分配**：按明细行顺序逐行分配拣货数量
2. **优先满足**：优先满足第一行的需求，剩余数量分配给后续行
3. **精确控制**：确保每行的拣货数量不超过计划数量
4. **关联记录**：通过`cr_dlv_sn_part_rmk6`字段记录具体分配到哪一行

#### 2.4 状态管理的业务流程
```mermaid
graph TD
    A[出库单创建] --> B[待拣货状态]
    B --> C[开始拣货]
    C --> D[逐个产品扫描]
    D --> E{是否全部拣完}
    E -->|否| D
    E -->|是| F[明细拣货完成]
    F --> G{所有明细是否完成}
    G -->|否| D
    G -->|是| H[单据拣货完成]
    H --> I[计算箱数]
    I --> J[准备品质检验]

    style B fill:#ffcc99
    style F fill:#99ccff
    style H fill:#99ff99
```

#### 2.5 关键业务规则详解

**2.5.1 状态控制机制**
- **出库单状态**：空（待拣货）→ "拣货完成"
- **明细状态**：空（待拣货）→ "拣货完成"
- **库存状态**：800（在库）→ 810（拣货）
- **渐进式完成**：支持部分拣货，明细级别的状态控制

**2.5.2 数量控制策略**
- **严格验证**：拣货数量 + 已拣数量 ≤ 计划数量
- **实时计算**：每次拣货前重新计算已拣数量
- **多维度控制**：按物料、仓库、明细行进行精确控制

**2.5.3 箱数计算业务逻辑**
```sql
-- 箱数计算逻辑（存在设计问题）
update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
    from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn
          from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
    left join (select part_no,count(sn_no) as ctn_num
               from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id)
               group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
    where tmp1.rn=1) tt
where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
```

**业务问题**：只更新每个物料的第一行明细，导致其他行的箱数信息缺失

#### 2.6 特殊客户处理逻辑（已注释）
```sql
-- 针对客户3031和3039的特殊产品编码处理
/*if _client_no='3031' or _client_no='3039' then
    _client_part_no := substring(split_part(_sn_no,'/',1),3);
    if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
        _part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
    end if;
end if;*/
```

**业务背景**：某些客户的产品编码规则特殊，需要动态调整匹配逻辑

### 三、代码逻辑深度分析

#### 3.1 查询函数的设计模式分析
**查询函数特点**：
```sql
-- af_query_wms_sales_outbound_list 的核心查询逻辑
select array_agg(row_to_json(tmp)) into tmp_json
from (select t1.part_no,t1.part_qty_plan::int,coalesce(t2.part_qty_real,0)::int as part_qty_real,t1.cr_dlv_h_no
    from (select ca.cr_dlv_h_no,cb.part_no,sum(cb.cr_dlv_qty_plan) as part_qty_plan
        from cr_dlv_h ca left join cr_dlv_b cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id
        where ca.cr_dlv_h_no=_bill_no group by ca.cr_dlv_h_no,cb.part_no) t1
    left join (select ca2.cr_dlv_h_no,cb2.part_no,sum(cb2.part_qty) as part_qty_real
            from cr_dlv_h ca2 left join cr_dlv_sn_part cb2 on cb2.cr_dlv_h_id=ca2.cr_dlv_h_id
            where ca2.cr_dlv_h_no=_bill_no group by ca2.cr_dlv_h_no,cb2.part_no) t2
        on t2.cr_dlv_h_no=t1.cr_dlv_h_no and t2.part_no=t1.part_no
    where t1.part_qty_plan>coalesce(t2.part_qty_real,0)) tmp;
```

**设计特点**：
- **双表关联汇总**：分别汇总计划数量和实际拣货数量
- **差异计算**：只返回还有拣货需求的物料
- **JSON格式化**：使用`array_agg(row_to_json())`返回结构化数据
- **实时计算**：每次查询都重新计算最新的拣货进度

#### 3.2 多行明细处理的复杂逻辑
```sql
-- 多行明细的智能分配算法
if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area having count(*)>1) then
    for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) loop
        if sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty >0 and _part_qty>0 then
            if (sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty)>=_part_qty then
                -- 当前行可以完全消化拣货数量
                update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty where cr_dlv_b_id=sub_datas.cr_dlv_b_id;
                -- 创建拣货记录，关联到当前明细行
                insert into public.cr_dlv_sn_part (..., cr_dlv_sn_part_rmk6, ...)
                select ..., sub_datas.cr_dlv_b_id, ... from pd_part where part_no=_part_no;
                _part_qty := 0; -- 拣货数量清零
            else
                -- 当前行只能部分消化，剩余继续下一行
                update public.cr_dlv_b set cr_dlv_qty=sub_datas.cr_dlv_qty_plan where cr_dlv_b_id=sub_datas.cr_dlv_b_id;
                insert into public.cr_dlv_sn_part (..., cr_dlv_sn_part_rmk6, ...)
                select ..., sub_datas.cr_dlv_b_id, ... from pd_part where part_no=_part_no;
                _part_qty := _part_qty+sub_datas.cr_dlv_qty-sub_datas.cr_dlv_qty_plan; -- 计算剩余数量
            end if;
        end if;
    end loop;
else
    -- 单行明细的简单处理
    select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
    insert into public.cr_dlv_sn_part (..., cr_dlv_sn_part_rmk6, ...)
    select ..., _b_id, ... from pd_part where part_no=_part_no;
    update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty where cr_dlv_b_id=_b_id;
end if;
```

**算法特点**：
- **循环分配**：按明细行顺序逐行分配拣货数量
- **精确控制**：确保每行不超量，剩余数量继续下一行
- **关联记录**：通过`cr_dlv_sn_part_rmk6`记录分配到的具体明细行
- **动态调整**：根据每行的剩余需求动态调整分配策略

#### 3.3 FIFO验证的代码实现分析
```sql
-- FIFO先进先出验证逻辑
if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
    select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
    if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type
              and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
        select min(substring(split_part(_sn_no,'/021748',2),1,4)) into _prod_cycle_min
        from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
        _err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',
                          _part_no,_sn_no,_prod_cycle,_prod_cycle_min);
        res := row('false', _err_msg);
        return to_json(res);
    end if;
end if;
```

**代码特点**：
- **条码解析**：使用`split_part`函数从条码中提取生产周期
- **字符串比较**：使用字符串比较判断批次先后（存在潜在问题）
- **类型匹配**：确保同一产品同一类型之间的FIFO控制
- **友好提示**：提供详细的错误信息，包含最早批次信息

#### 3.4 状态同步的事务控制
```sql
-- 多表状态同步更新
-- 1. 更新明细状态
update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;

-- 2. 检查并更新单头状态
if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
    update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
    where cr_dlv_h_id=_bill_id;
end if;

-- 3. 更新库存状态
update public.wm_sn set sn_status='810',sn_status_name='拣货',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
where sn_no=_sn_no;
```

**事务特点**：
- **原子性**：所有更新在同一事务中，失败会全部回滚
- **一致性**：确保库存状态与出库单状态的同步
- **审计信息**：记录操作用户和时间信息

#### 3.5 箱数计算逻辑的问题分析
```sql
-- 存在问题的箱数更新逻辑
update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
    from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn
          from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
    left join (select part_no,count(sn_no) as ctn_num
               from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id)
               group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
    where tmp1.rn=1) tt  -- 问题：只更新第一行
where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
```

**问题分析**：
- **设计缺陷**：`where tmp1.rn=1`导致只更新每个物料的第一行
- **数据不一致**：同一物料的其他明细行箱数信息缺失
- **下游影响**：可能影响SAP接口或报表的数据准确性

#### 3.6 异常处理机制
```sql
EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;

    _err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text); -- 存在错误：重复使用_err_msg_text
    res := row('false',_err_msg);
    return to_json(res);
```

**问题**：错误信息格式化中重复使用了`_err_msg_text`，应该使用`_err_pg_detail`

### 四、表关联逻辑深度分析

#### 4.1 核心表结构关系图
```mermaid
erDiagram
    wm_sn ||--o{ cr_dlv_sn_part : "1对多"
    cr_dlv_h ||--o{ cr_dlv_b : "1对多"
    cr_dlv_h ||--o{ cr_dlv_sn_part : "1对多"
    cr_dlv_b ||--o{ cr_dlv_sn_part : "1对多"
    pd_part ||--o{ wm_sn : "1对多"
    ss_user ||--o{ cr_dlv_sn_part : "1对多"

    wm_sn {
        varchar sn_no PK "序列号"
        varchar part_no FK "物料编码"
        varchar sn_status "库存状态"
        varchar sn_type "序列号类型"
        varchar inventory_lot "库存批次"
        date produce_date "生产日期"
        varchar invp_area_no "仓库区域"
        decimal part_qty "数量"
    }

    cr_dlv_h {
        varchar cr_dlv_h_id PK "出库单ID"
        varchar cr_dlv_h_no UK "出库单号"
        varchar cr_dlv_h_rmk6 "单据状态"
        varchar client_no "客户编号"
    }

    cr_dlv_b {
        varchar cr_dlv_b_id PK "明细ID"
        varchar cr_dlv_h_id FK "出库单ID"
        varchar part_no "物料编码"
        varchar invp_no "仓库编码"
        decimal cr_dlv_qty_plan "计划数量"
        decimal cr_dlv_qty "实际数量"
        varchar cr_dlv_b_rmk5 "箱数"
        varchar cr_dlv_b_rmk6 "明细状态"
    }

    cr_dlv_sn_part {
        varchar cr_dlv_sn_part_id PK "拣货记录ID"
        varchar cr_dlv_h_id FK "出库单ID"
        varchar sn_no FK "序列号"
        varchar part_no "物料编码"
        varchar invp_no "仓库编码"
        decimal part_qty "拣货数量"
        varchar cr_dlv_sn_part_rmk6 "关联明细ID"
    }

    pd_part {
        varchar part_no PK "物料编码"
        varchar part_name "物料名称"
        varchar part_spec "规格"
        boolean is_fifo "是否FIFO"
    }
```

![1751458505216](D:\金洋\成品出入库记录\出库\assets\1751458505216.png)

#### 4.2 关联查询的性能分析

**查询函数的关联逻辑**：

```sql
-- 计划数量汇总（cr_dlv_h + cr_dlv_b）
select ca.cr_dlv_h_no,cb.part_no,sum(cb.cr_dlv_qty_plan) as part_qty_plan
from cr_dlv_h ca left join cr_dlv_b cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id
where ca.cr_dlv_h_no=_bill_no group by ca.cr_dlv_h_no,cb.part_no

-- 实际拣货汇总（cr_dlv_h + cr_dlv_sn_part）
select ca2.cr_dlv_h_no,cb2.part_no,sum(cb2.part_qty) as part_qty_real
from cr_dlv_h ca2 left join cr_dlv_sn_part cb2 on cb2.cr_dlv_h_id=ca2.cr_dlv_h_id
where ca2.cr_dlv_h_no=_bill_no group by ca2.cr_dlv_h_no,cb2.part_no
```

**性能特点**：
- **双重汇总**：分别汇总计划和实际数量，然后关联比较
- **索引需求**：需要在`cr_dlv_h_no`、`cr_dlv_h_id`、`part_no`上建立索引
- **实时计算**：每次查询都重新计算，确保数据实时性

#### 4.3 关键关联字段详解
**主键关联**：
- **`sn_no`**：序列号，连接库存表和拣货记录表
- **`cr_dlv_h_id`**：出库单ID，连接单头、明细、拣货记录
- **`cr_dlv_b_id`**：明细ID，通过`cr_dlv_sn_part_rmk6`字段存储

**业务关联**：
- **`part_no`**：物料编码，多表共用的业务主键
- **`invp_no`**：仓库编码，确保仓库匹配
- **`client_no`**：客户编码，用于特殊业务规则

#### 4.4 数据完整性约束分析
**现有约束机制**：
1. **事务控制**：整个拣货过程在一个事务中，确保原子性
2. **状态同步**：库存状态与出库单状态同步更新
3. **数量一致性**：明细实际数量等于拣货记录汇总
4. **业务验证**：通过应用层验证确保业务规则

**缺失约束**：
- 缺少数据库层面的外键约束
- 缺少字段级别的检查约束
- 缺少数据类型和范围限制

#### 4.5 表关联的复杂查询模式
**多表关联的拣货验证**：
```sql
-- 验证产品是否属于出库单
if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) then
    -- 拒绝拣货
end if;

-- FIFO验证需要的复杂查询
if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type
          and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
    -- 存在更早批次，拒绝拣货
end if;
```

#### 4.6 数据流转的表间关系
```mermaid
graph TD
    A[扫描条码] --> B[查询wm_sn]
    B --> C[验证cr_dlv_h状态]
    C --> D[验证cr_dlv_b归属]
    D --> E[检查pd_part.is_fifo]
    E --> F[创建cr_dlv_sn_part记录]
    F --> G[更新cr_dlv_b数量]
    G --> H[更新wm_sn状态]
    H --> I[更新cr_dlv_h状态]

    subgraph "数据验证层"
        J[库存状态验证]
        K[出库单状态验证]
        L[数量控制验证]
        M[FIFO规则验证]
    end

    B --> J
    C --> K
    D --> L
    E --> M
```

![1751458544063](D:\金洋\成品出入库记录\出库\assets\1751458544063.png)

#### 4.7 关联表的扩展性设计

**备注字段的使用**：
- **`cr_dlv_h_rmk6`**：存储单据状态（空 → "拣货完成"）
- **`cr_dlv_b_rmk5`**：存储箱数信息
- **`cr_dlv_b_rmk6`**：存储明细状态（空 → "拣货完成"）
- **`cr_dlv_sn_part_rmk6`**：存储关联的明细ID

**设计优势**：
- 灵活性高，便于业务扩展
- 无需修改表结构即可支持新需求

**设计劣势**：
- 字段语义不明确
- 缺少数据类型约束
- 不利于数据库优化

### 五、实际应用场景分析

#### 5.1 典型拣货作业流程
```
1. 拣货员扫描出库单号 → 调用查询函数获取待拣货物料列表
2. 按物料逐个扫描产品条码 → 系统验证并创建拣货记录
3. 系统自动分配到具体明细行 → 更新拣货数量和状态
4. 完成所有物料拣货 → 系统计算箱数并更新单据状态
5. 单据状态变为"拣货完成" → 准备进入品质检验环节
```

#### 5.2 复杂场景处理能力
**多行明细场景**：
- 同一物料对应多个销售订单
- 不同交期要求的分批拣货
- 智能分配算法确保精确控制

**FIFO管控场景**：
- 电子产品的批次管理
- 有保质期要求的产品
- 基于生产周期的先进先出

**多仓库协同场景**：
- 同一订单涉及多个仓库
- 按仓库分别进行拣货作业
- 仓库级别的数量控制

#### 5.3 系统设计的优势与不足

**优势**：
1. **精确的数量控制**：多层验证确保拣货数量准确
2. **灵活的明细分配**：支持复杂的多行明细场景
3. **完整的状态管理**：从库存到单据的全程状态跟踪
4. **实时的进度反馈**：查询函数提供实时的拣货进度
5. **严格的FIFO控制**：确保先进先出的业务要求

**不足与风险**：
1. **箱数更新缺陷**：只更新第一行导致数据不完整
2. **FIFO算法局限**：字符串比较可能存在精度问题
3. **性能优化空间**：复杂查询和循环逻辑有优化空间
4. **错误处理不完善**：异常信息格式化存在问题
5. **缺乏撤销机制**：无法处理误操作的情况

### 六、综合优化建议

#### 6.1 数据层优化
1. **建立合适的索引**：
   ```sql
   CREATE INDEX idx_cr_dlv_h_no_rmk6 ON cr_dlv_h(cr_dlv_h_no, cr_dlv_h_rmk6);
   CREATE INDEX idx_cr_dlv_b_h_part_invp ON cr_dlv_b(cr_dlv_h_id, part_no, invp_no);
   CREATE INDEX idx_cr_dlv_sn_part_h_part ON cr_dlv_sn_part(cr_dlv_h_id, part_no);
   CREATE INDEX idx_wm_sn_part_status ON wm_sn(part_no, sn_status, sn_type);
   ```

2. **增加数据约束**：
   ```sql
   ALTER TABLE cr_dlv_h ADD CONSTRAINT chk_h_rmk6_status
   CHECK (cr_dlv_h_rmk6 IN ('', '拣货完成'));

   ALTER TABLE cr_dlv_b ADD CONSTRAINT chk_b_rmk6_status
   CHECK (cr_dlv_b_rmk6 IN ('', '拣货完成'));

   ALTER TABLE wm_sn ADD CONSTRAINT chk_sn_status
   CHECK (sn_status IN ('800', '810', '820'));
   ```

#### 6.2 业务逻辑优化
1. **修复箱数更新问题**：
   ```sql
   -- 修改为更新所有相关行
   update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
   from (select part_no, count(distinct sn_no) as ctn_num
         from cr_dlv_sn_part where cr_dlv_h_id=_bill_id group by part_no) tt
   where cr_dlv_b.cr_dlv_h_id=_bill_id and cr_dlv_b.part_no=tt.part_no;
   ```

2. **优化FIFO逻辑**：
   ```sql
   -- 改为基于日期的比较
   if exists(select 1 from wm_sn where part_no=_part_no and sn_type=_sn_type
             and produce_date::date < _produce_date and sn_status='800') then
       select min(produce_date)::date into _produce_date_min
       from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
   end if;
   ```

3. **增加撤销功能**：
   ```sql
   CREATE OR REPLACE FUNCTION af_pda_wms_sales_outbound_picking_cancel(datas character varying)
   RETURNS character varying AS $$
   -- 实现拣货撤销逻辑
   $$;
   ```

#### 6.3 代码逻辑优化
1. **修复错误处理**：
   ```sql
   _err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_pg_detail);
   ```

2. **增加操作日志**：
   ```sql
   INSERT INTO operation_log(operation_type, bill_no, sn_no, user_no, operation_time, operation_result)
   VALUES('PICKING', _bill_no, _sn_no, _user_no, localtimestamp, 'SUCCESS');
   ```

3. **优化循环逻辑**：
   - 考虑使用CTE替代循环
   - 减少不必要的数据库访问

#### 6.4 表设计优化
1. **规范化状态字段**：
   - 将状态字段改为枚举类型
   - 建立状态转换规则表

2. **增加审计字段**：
   - 记录创建人、修改人信息
   - 增加操作时间戳

3. **优化备注字段使用**：
   - 明确各备注字段的业务含义
   - 考虑拆分为专门的扩展表

#### 6.5 性能优化建议
- **查询优化**：使用更高效的查询语句
- **批量处理**：支持批量拣货操作
- **缓存机制**：对频繁查询的数据进行缓存
- **分区表**：对大数据量表考虑分区策略

#### 6.6 业务功能增强
- **拣货路径优化**：根据仓库布局优化拣货顺序
- **效率统计**：记录拣货效率和准确率
- **移动端优化**：优化PDA界面和操作流程
- **异常处理**：完善各种异常情况的处理机制
- **集成接口**：与WCS、TMS等系统的集成

### 七、总结

PDA销售出库拣货系统是WMS的核心功能模块，通过精确的数量控制、灵活的明细分配和完整的状态管理，实现了复杂业务场景下的高效拣货作业。系统设计体现了制造业对精细化管理的要求，但在箱数更新、FIFO算法和性能优化方面还有进一步提升的空间。

**核心价值**：
- 实现了有序列号产品的精确拣货管控
- 支持复杂的多行明细智能分配
- 建立了完整的拣货作业追溯体系

**改进方向**：
- 修复箱数更新的设计缺陷
- 优化FIFO算法的实现方式
- 完善错误处理和异常机制
- 增强系统的性能和可扩展性
