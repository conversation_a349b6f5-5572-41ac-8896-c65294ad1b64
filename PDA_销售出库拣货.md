单据编号校验：af_query_wms_sales_outbound_list

```sql
-- DROP FUNCTION public.af_query_wms_sales_outbound_list(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_query_wms_sales_outbound_list(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

 \* 功能：根据 销售出库单号查询明细，并返回销售订单+物料编码的组合码

 \* 描述：

 \* 时间:

 \* 开发者：

 */

​	**declare** 

​		jsondatas **json**;

​		_bill_no **text**;

​		tmp_json **json**[];



​	**begin**

​		jsondatas := **json**(datas);

​		**raise** **notice** '%',jsondatas;

​	

​		jsondatas := **json**(jsondatas->'datas'->0);

​		_bill_no := jsondatas->>'bill_no';



​		**insert** **into** public.a_test_log **values**(datas,'query_wms_sales_outbound_list',**localtimestamp**);

​	

​		/*select array_agg(row_to_json(tmp)) into tmp_json 

​		from (select cb.part_no,cb.cr_dlv_qty_plan,cb.so_h_no

​			from cr_dlv_h ca

​			left join cr_dlv_b cb on cb.cr_dlv_h_id=ca.cr_dlv_h_id

​			where ca.cr_dlv_h_no=_bill_no and cb.cr_dlv_qty_plan>coalesce(cb.cr_dlv_qty,0)

​			order by cb.part_no,cb.so_h_no

​			) tmp;*/

​			

​		**select** **array_agg**(**row_to_json**(tmp)) **into** tmp_json 

​		**from** (**select** t1.part_no,t1.part_qty_plan::**int**,**coalesce**(t2.part_qty_real,0)::**int** **as** part_qty_real,t1.cr_dlv_h_no

​			**from** (**select** ca.cr_dlv_h_no,cb.part_no,**sum**(cb.cr_dlv_qty_plan) **as** part_qty_plan 

​				**from** cr_dlv_h ca

​				**left** **join** cr_dlv_b cb **on** cb.cr_dlv_h_id=ca.cr_dlv_h_id

​				**where** ca.cr_dlv_h_no=_bill_no

​				**group** **by** ca.cr_dlv_h_no,cb.part_no) t1

​			**left** **join** (**select** ca2.cr_dlv_h_no,cb2.part_no,**sum**(cb2.part_qty) **as** part_qty_real

​					**from** cr_dlv_h ca2

​					**left** **join** cr_dlv_sn_part cb2 **on** cb2.cr_dlv_h_id=ca2.cr_dlv_h_id

​					**where** ca2.cr_dlv_h_no=_bill_no

​					**group** **by** ca2.cr_dlv_h_no,cb2.part_no) t2 **on** t2.cr_dlv_h_no=t1.cr_dlv_h_no **and** t2.part_no=t1.part_no

​			**where** t1.part_qty_plan>**coalesce**(t2.part_qty_real,0)

​			**order** **by** t1.part_no

​			) tmp;

​		**return** **json_build_object**('successful',**true**,'msg','查询成功','datas',tmp_json);	

​	

​	**END**;

**$function$**

;


```



销售出库拣货：

```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库拣货
 * 描述   ：
 * 时间   ：
 * 开发者 ：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;
	
		_sn_type text;
		_produce_date date;
		_produce_date_min date;
	
		sub_datas record;
		_prod_lot text;
		_b_id text;
		_invp_area text;
		_client_no text;
		_client_part_no text;
		_prod_cycle text;
		_prod_cycle_min text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	begin
		json_datas := json(datas);	
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		--_part_no := json_datas->>'part_no';	
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		select part_no,part_qty,sn_type,inventory_lot,produce_date::date,invp_area_no into _part_no,_part_qty,_sn_type,_prod_lot,_produce_date,_invp_area  
		from wm_sn where sn_no=_sn_no;
	
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			_err_msg := format('扫描销售出库单【%s】，不是待拣货状态。',_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		select cr_dlv_h_id,client_no into _bill_id,_client_no from cr_dlv_h where cr_dlv_h_no=_bill_no;

		/*
		if _client_no='3031' or _client_no='3039' then
			_client_part_no := substring(split_part(_sn_no,'/',1),3);
			if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
				_part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
			end if;
		end if;
		*/

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】仓库【%s】）不属于此销售出库单【%s】限定的产品与仓库，不能拣货。',_part_no,_sn_no,_invp_area,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);		
		end if;
	
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='800') then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】）不是在库状态，不能拣货。',_part_no,_sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		----------------------------------------------------------------------------------------
		if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
			select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
			--if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and produce_date::date<_produce_date and sn_status='800') then
			if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
				--select min(produce_date)::date into _produce_date_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
				select min(substring(split_part(_sn_no,'/021748',2),1,4)) into _prod_cycle_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';				
				_err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',_part_no,_sn_no,_prod_cycle,_prod_cycle_min);
				res := row('false', _err_msg);
				return to_json(res);
			end if;
		end if;
		----------------------------------------------------------------------------------------
	
		if exists(select 1 from cr_dlv_sn_part where sn_no=_sn_no) then
			res := row('false', '扫描产品条码已经拣货，不能二次拣货。');
			return to_json(res);
		end if;
	
		select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
		if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
			_err_msg := format('产品（物料编码【%s】）拣货数量(已拣货数量【%s】+现拣货数量【%s】)大于销售出库单【%s】需要求数量【%s】，不能拣货。',_part_no,coalesce(_part_qty_real,0),_part_qty::int,_bill_no,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		---------------------------------------------------------------------------------------
		if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area having count(*)>1) then
			for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area) loop
				if 	sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty >0 and _part_qty>0 then	
					if (sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty)>=_part_qty then
						update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := 0;
					else
						update public.cr_dlv_b set cr_dlv_qty=sub_datas.cr_dlv_qty_plan
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := _part_qty+sub_datas.cr_dlv_qty-sub_datas.cr_dlv_qty_plan;
					end if;
				end if;

			end loop;
		else
			select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no and invp_no=_invp_area;
			insert into public.cr_dlv_sn_part
			(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', _b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from pd_part
			where part_no=_part_no;

			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
			where cr_dlv_b_id=_b_id;
		end if;
		---------------------------------------------------------------------------------------

		update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;
		
		---------------------------------------------------------------------------------------

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
			update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where cr_dlv_h_id=_bill_id; 

			update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
			from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
				from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
				left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
				where tmp1.rn=1) tt
			where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
		end if;
	
		update public.wm_sn set sn_status='810',sn_status_name='拣货',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;
		---------------------------------------------------------------------------------------
		res := row('true', '销售出库拣货完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;


```


功能逻辑：

## 一、数据逻辑分析

### 1. 数据流转路径
```
扫描条码(sn_no) → 查询wm_sn库存信息 → 验证出库单信息 → 创建拣货记录 → 更新库存状态
                                                    ↓
                                            更新出库单明细数量 → 计算箱数
```

### 2. 核心数据处理流程
1. **条码扫描验证**：通过`sn_no`查询`wm_sn`表获取产品信息
2. **出库单验证**：验证出库单状态必须为待拣货（`cr_dlv_h_rmk6=''`）
3. **库存验证**：验证产品必须为在库状态（`sn_status='800'`）
4. **FIFO验证**：如果产品启用先进先出，验证是否有更早批次
5. **数量验证**：验证拣货数量不超过计划数量
6. **拣货记录创建**：在`cr_dlv_sn_part`表创建拣货记录
7. **状态更新**：更新库存状态和出库单状态

## 二、业务逻辑分析

### 1. 业务场景
- **有序列号管控产品的销售出库拣货**
- 支持按条码逐个扫描拣货
- 支持多行明细的智能分配

### 2. 关键业务规则
1. **状态控制**
   - 出库单必须是待拣货状态
   - 产品必须是在库状态（800）
   - 拣货后更新为拣货状态（810）

2. **FIFO管理**
   - 检查`pd_part.is_fifo`字段
   - 使用条码中的生产周期字段（`/021748`后4位）进行比较
   - 确保先出库早期生产的产品

3. **数量控制**
   - 严格控制拣货数量不超过计划数量
   - 支持同一物料多行明细的智能分配

4. **箱数计算**
   - 拣货完成后计算每个物料的总箱数
   - 只更新每个物料第一行的`cr_dlv_b_rmk5`字段（**潜在问题**）

### 3. 特殊客户处理
- 客户编号3031和3039的特殊处理逻辑（已注释）

## 三、代码逻辑分析

### 1. 多行明细处理逻辑
```sql
-- 当同一物料存在多行明细时的处理
if exists(select 1 from cr_dlv_b where ... having count(*)>1) then
    -- 循环处理每行明细，按需分配拣货数量
    for sub_datas in (...) loop
        if 剩余需求 >= 当前拣货数 then
            全部分配到当前行
        else
            部分分配，剩余继续下一行
        end if
    end loop
else
    -- 单行明细直接处理
end if
```

### 2. 箱数更新逻辑（存在问题）
```sql
update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
from (select ... row_number() over(partition by part_no) as rn ...)
where tmp1.rn=1  -- 只更新第一行
```
**问题**：当同一物料有多行明细时，只有第一行会更新箱数，其他行为空或旧值

### 3. 状态流转
- 库存状态：800（在库）→ 810（拣货）
- 明细状态：空 → "拣货完成"
- 单头状态：空 → "拣货完成"（所有明细完成时）

## 四、表关联逻辑分析

### 1. 核心表关系
```mermaid
graph TD
    A[wm_sn 序列号库存] -->|sn_no| B{拣货验证}
    C[cr_dlv_h 出库单头] -->|cr_dlv_h_id| D[cr_dlv_b 出库单明细]
    B -->|通过| E[cr_dlv_sn_part 拣货记录]
    D -->|cr_dlv_b_id| E
    F[pd_part 物料主数据] -->|is_fifo| B
    
    style A fill:#ffcc99,stroke:#333,stroke-width:2px
    style C fill:#ff9999,stroke:#333,stroke-width:2px
    style D fill:#99ccff,stroke:#333,stroke-width:2px
    style E fill:#99ff99,stroke:#333,stroke-width:2px
```

### 2. 关键关联字段
- `sn_no`：连接库存和拣货记录
- `cr_dlv_h_id`：连接单头和明细、拣货记录
- `cr_dlv_b_id`：通过`cr_dlv_sn_part_rmk6`存储，关联明细和拣货记录
- `part_no`：物料编码，多表共用

### 3. 数据完整性保证
1. **事务控制**：整个函数在一个事务中，失败会回滚
2. **状态同步**：库存状态和出库单状态同步更新
3. **数量一致性**：明细实际数量等于拣货记录汇总

## 五、问题与优化建议

### 1. 箱数更新问题
**现状**：只更新每个物料第一行的箱数
**影响**：导致SAP接口数据分组错误
**建议**：
```sql
-- 修改为更新所有相关行
update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
from (select part_no, count(distinct sn_no) as ctn_num ...) tt
where cr_dlv_b.cr_dlv_h_id=_bill_id 
  and cr_dlv_b.part_no=tt.part_no; -- 更新所有匹配的行
```

### 2. FIFO逻辑优化
- 当前使用字符串比较生产周期，建议改为日期比较
- 考虑增加索引优化查询性能

### 3. 性能优化
- 对高频查询的字段增加索引
- 考虑批量处理优化循环逻辑

### 4. 业务增强
- 增加拣货撤销功能
- 支持部分拣货的暂存功能
- 增加拣货效率统计
