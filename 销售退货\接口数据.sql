--销售退货(SAP接口请求数据)
select 
sa.cr_rtn_h_no,
json_build_object(
'CardCode',sa.client_no,
'DocDate',to_char(sa.upd_time, 'yyyy-mm-dd'),
'OwnerCode','379',
'U_WebNo',sa.cr_rtn_h_no,
'details',
json_agg(json_build_object('ItemCode',sb.part_no,'QuanTity',sb.part_Qty_real,'BatchNo',sb.lot_no,'WhsCode',sb.invp_area,'PriceAfterVAT',sb.price_afvat,'Currency',sb.currency,'EnSetCost',sb.price,'RetCost',sb.stock_price,'U_BaseType','销售交货','U_QtDocNum',sb.so_h_no,'U_QtLine',split_part(sb.so_b_id,'_',2),'U_KHPO',sb.u_khpo)) 
) as datas
from szjy_mes_cr_rtn_h sa
left join szjy_mes_cr_rtn_b sb on sb.cr_rtn_h_id=sa.cr_rtn_h_id
where sa.cr_rtn_rmk06 ='仓库扫描收货完成'
group by sa.cr_rtn_h_no,sa.client_no,sa.upd_time

