```sql

-- 销售出货(SAP申请单)


   SELECT cr_dlv_h_id,cr_dlv_h_no,cr_dlv_h_rmk7,status_name,cr_dlv_datetime,client_no,client_name,cr_dlv_h_rmk1,cr_dlv_h_rmk3,cr_dlv_h_rmk6,cr_dlv_type,emp_id,sap_bill_no,kh_contacter,cr_shipping_addr FROM (SELECT a.*,
	b.cdvl_no as status_name
FROM cr_dlv_h a
LEFT JOIN ss_cdvl b ON a.cr_dlv_h_status = b.cdvl_no and b.cdtp_id='ss_status_200'
order by cr_dlv_datetime desc
) Tb  WHERE 1=1 




```