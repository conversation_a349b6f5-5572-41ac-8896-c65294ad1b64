CREATE TABLE public.szjy_mes_cr_rtn_h (
	cr_rtn_h_id text DEFAULT af_auid() NOT NULL,
	cr_rtn_h_no text DEFAULT af_ss_no_generate('szjy_sal_th_no'::character varying) NOT NULL,
	cr_rtn_datetime date DEFAULT CURRENT_DATE NOT NULL,
	client_no text NOT NULL,
	client_name text NULL,
	so_h_no text NULL,
	currency text NULL,
	need_new_sn bool DEFAULT true NOT NULL,
	cr_rtn_rmk01 text NULL,
	cr_rtn_rmk02 text NULL,
	cr_rtn_rmk03 text NULL,
	cr_rtn_rmk04 text NULL,
	cr_rtn_rmk05 text NULL,
	cr_rtn_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT szjy_mes_cr_rtn_h_pk PRIMARY KEY (cr_rtn_h_id)
);



CREATE TABLE public.szjy_mes_cr_rtn_b (
	cr_rtn_b_id text DEFAULT af_auid() NOT NULL,
	cr_rtn_h_id text NULL,
	part_no text NOT NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_qty_plan numeric DEFAULT 0 NOT NULL,
	part_qty_real numeric DEFAULT 0 NOT NULL,
	lot_no text NULL,
	invp_area text NULL,
	so_h_no text NULL,
	so_b_id text NULL,
	price numeric DEFAULT 0 NOT NULL,
	currency text NULL,
	price_afvat numeric DEFAULT 0 NOT NULL,
	stock_price numeric DEFAULT 0 NOT NULL,
	u_khpo text NULL,
	cr_rtn_rmk01 text NULL,
	cr_rtn_rmk02 text NULL,
	cr_rtn_rmk03 text NULL,
	cr_rtn_rmk04 text NULL,
	cr_rtn_rmk05 text NULL,
	cr_rtn_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT szjy_mes_cr_rtn_b_pk PRIMARY KEY (cr_rtn_b_id)
);