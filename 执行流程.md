调用流程：

```mermaid
graph TD
    A[接口同步SAP出库申请单] -->|同步列表| B(销售出库_SAP申请_列表)
    B --> C[有序列号]
    C-->E[PDA_销售出货拣货]
    E-->Q[PDA_销售出库扫描品质]
    Q-->J[PDA_销售出库装车]
     J-->T[接口_MES-SAP销售发货单]
    T-->Y[销售出库完成]
  
    
    B -->D[无序列号]
    D-->F[PDA_销售出库_无序列号_]
    F-->T

```

分析思路：分析从数据逻辑、业务逻辑、代码逻辑、表直接的关联逻辑去分析。

​    表关系：
```mermaid
graph TD
    subgraph "核心出库单据"
        A[cr_dlv_h 出库单头]
        B[cr_dlv_b 出库单体]
        C[cr_dlv_sn_part 出库拣货记录]
    end
    
    subgraph "库存源数据"
        D[wm_sn 序列号库存]
        E[szjy_wm_inventory 批次库存]
    end
    
    subgraph "基础及其他"
        F[pd_part 物料主数据]
        G[ss_user 用户]
        H[cr_rtn_h 销售退货]
    end

    A --"1..N"--> B
    A --"1..N"--> C
    B --"1..N"--> C

    D --"有序列号拣货"--> C
    E --"无序列号出库"--> C
    
    B --> F
    D --> F
    E --> F
    A --> G

    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#99ff99
```

![1751423216415](D:\金洋\成品出入库记录\出库\assets\1751423216415.png)

