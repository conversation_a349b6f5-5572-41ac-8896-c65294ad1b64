-- DROP FUNCTION public.af_pda_me_finish_multi_scan(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_me_finish_multi_scan(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
*
*
*
*
*/
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;

		_sn_no text;
		_part_no text;
		_mo_cnt int;
		_box_cnt int;
		_lbl_cnt int;
		_box_type_cnt int;
		_box_sub_cnt int;
		_box_sub_type text;
		_finish_qty int;

		_mo_no text;
		_ea_no text;
		_ea_name text;
		_me_finish_io_id_h text;
		_si_lot_h_id text;
		_me_finish_io_no text;
		_si_lot_h_no text;
	
		row_datas record;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;	

	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas');

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		create temp table temp_table_rksn as (select a.ea_no,a.sn_no,b.mo_no,b.part_no,b.part_qty  
											from json_to_recordset(json_datas)as a(sn_no text, ea_no text)
											left join wm_sn b on b.sn_no=a.sn_no);

		select count(mo_no) into _mo_cnt from (select distinct mo_no from temp_table_rksn);
		if _mo_cnt != 1 then
			res := row('false', '多工单扫描不可以提交生成入库单');
			return to_json(res);
		end if;		

		select distinct a.ea_no,a.mo_no,e.ea_name into _ea_no,_mo_no,_ea_name 
		from temp_table_rksn a
		left join ea e on e.ea_no = a.ea_no;

		select part_no,count(sn_no) into _part_no,_lbl_cnt from temp_table_rksn group by part_no;

		if not exists(select 1 from szjy_multi_subitem_h where part_no_m=_part_no) then
			_err_msg := format('扫描产品【%s】不是多件套产品，不可以使用MES多件套入库扫描。', _part_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		select box_num into _box_cnt from szjy_multi_subitem_h where part_no_m=_part_no;

		if mod(_lbl_cnt, _box_cnt) != 0 then
			_err_msg := format('扫描标签的数量【%s】不是多件套套装箱数【%s】的整数倍。', _lbl_cnt, _box_cnt);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		_finish_qty := _lbl_cnt/_box_cnt;

		create temp table temp_tb_statis as (select ma.part_no,mb.mt_box_type,count(ma.sn_no) as sub_cnt
						from temp_table_rksn ma left join szjy_multi_scaninfo mb on mb.sn_no_box=ma.sn_no and mb.box_type='外箱'
						group by ma.part_no,mb.mt_box_type);

		select count(mt_box_type) into _box_type_cnt 
		from (select distinct ma.part_no,mb.mt_box_type
						from temp_table_rksn ma left join szjy_multi_scaninfo mb on mb.sn_no_box=ma.sn_no and mb.box_type='外箱');

		if _box_type_cnt != _box_cnt then
			_err_msg := format('扫描产品标签箱类型数量【%s】不是多件套套装定义箱数【%s】。', _box_type_cnt, _box_cnt);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		
		_box_sub_cnt := 0;
		for row_datas in (select * from temp_tb_statis) loop
			if _box_sub_cnt = 0 then
				_box_sub_cnt := row_datas.sub_cnt;
				_box_sub_type := row_datas.mt_box_type;
			else
				if _box_sub_cnt != row_datas.sub_cnt then
					_err_msg := format('扫描产品不是整套产品（子箱数量不相同，子箱【%s】数量【%s】，子箱【%s】数量【%s】）。', _box_sub_type, _box_sub_cnt,row_datas.mt_box_type,row_datas.sub_cnt);
					res := row('false', _err_msg);
					return to_json(res);
				end if;
			end if;		
		end loop;

	-----------------------------------------------------------

    	_me_finish_io_id_h:=af_auid();
    	_si_lot_h_id:=af_auid();
    	_me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    	_si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);

		--写入库单表头    
    	insert into public.me_finish_io_h
			(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
			mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
			invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
			invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
			me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
			fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, mo_type)
		values ( 
			_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
			_mo_no, _part_no, '', '', '', '', '', _finish_qty, 
			'', 0, '', 0, '', 0, 
			'', _ea_no, _user_no, _user_name, 
			_ea_name, '20', '', '', 
			'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
			false, 0, null, '');

     	---写入表明细
     	insert into public.me_finish_io
			(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
			part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
			finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
			crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, sn_no)		
		select af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,ws.part_spec ,
			pp.part_unit ,'','',ws.part_qty ,'',0,'',0,'',
			0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
		from temp_table_rksn a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit;
	

 		insert into qm_si_lot_h(
			si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
			part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
			move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
			order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
			si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
			si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
			si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
			si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
			da_switch_id,ea_no,ea_name,
			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
			upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
			io_is_sucessed,io_times,io_last_time)
		select 
			_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE',m.factory_no,sf.factory_name,m.part_no,p.part_name,
			p.part_spec,null,'','',_finish_qty,'310','完工检验',null,null,
			_me_finish_io_id_h,_me_finish_io_no,'MO','生产订单',_me_finish_io_id_h,null,null,m.mo_no ,m.client_no,
			m.client_name,null,null,'20','抽检','20','正常','10','一般','',0,0,
			null,null,false,null,null,
			null,null,null,null,null,null,
			null,null,null,null,null,null,
			null,_ea_no,_ea_name,
			now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			false,0,null
		from (select * from mo where mo_no=_mo_no) m
		left join ss_factory sf on sf.factory_no = m.factory_no
		left join pd_part p on p.part_no=m.part_no;


		--写入送检单条码明细
		INSERT INTO public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
			si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
			si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
			mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
			sn_type,sn_type_name)
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_rksn a
		left join wm_sn ws on a.sn_no = ws.sn_no;
		/*union all 
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_rksn a
		left join szjy_multi_scaninfo b on b.sn_no_box=a.sn_no and b.box_type='子件'
		left join wm_sn ws on ws.sn_no = b.sn_no;*/

		-------------------------------------------------------------------------------------
				

		res := row('true', '生成入库单完成.');
		return to_json(res);
	exception when others then
		GET STACKED diagnostics
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;
