



## 销售发货相关表

### 1. **cr_dlv_h** - 销售发货单头表
- **作用**：存储销售发货单的主要信息
- **关键字段**：
  - `cr_dlv_h_id`：发货单ID
  - `cr_dlv_h_no`：发货单号
  - `client_no`：客户编号
  - `cr_dlv_type`：发货类型（订单/其他）
  - `cr_dlv_h_rmk6`：状态标记（发货完成、拣货完成等）
  - `sap_bill_no`：SAP单据号
  - `cr_dlv_h_status`：发货单状态

### 2. **cr_dlv_b** - 销售发货单明细表
- **作用**：存储发货单的产品明细信息
- **关键字段**：
  - `cr_dlv_b_id`：明细ID
  - `cr_dlv_h_id`：关联发货单头表
  - `part_no`：物料编码
  - `cr_dlv_qty_plan`：计划发货数量
  - `cr_dlv_qty`：实际发货数量
  - `price`：价格
  - `so_h_no`：销售订单号
  - `so_b_id`：销售订单明细ID
  - `invp_no`：仓库编码
  - `cr_dlv_b_rmk5`：箱数
  - `cr_dlv_b_rmk6`：明细状态（拣货完成、发货完成等）

### 3. **cr_dlv_sn_part** - 销售发货序列号明细表
- **作用**：记录发货产品的序列号和批次信息
- **关键字段**：
  - `cr_dlv_sn_part_id`：序列号明细ID
  - `cr_dlv_h_id`：关联发货单头表
  - `sn_no`：序列号
  - `part_no`：物料编码
  - `lot_no`：批次号
  - `invp_no`：仓库编码
  - `part_qty`：数量
  - `cr_dlv_sn_part_rmk6`：关联发货明细ID

## 库存管理相关表

### 4. **szjy_wm_inventory** - 仓库库存表
- **作用**：管理仓库库存数量和批次信息
- **关键字段**：
  - `inventory_id`：库存ID
  - `part_no`：物料编码
  - `invp_area`：仓库区域
  - `part_qty`：库存数量
  - `lot_no`：批次号

### 5. **wm_sn** - 序列号管理表
- **作用**：管理产品序列号的状态和位置信息
- **关键字段**：
  - `sn_no`：序列号
  - `part_no`：物料编码
  - `part_qty`：数量
  - `sn_status`：序列号状态（800-在库，810-拣货等）
  - `sn_type`：序列号类型
  - `inventory_lot`：库存批次
  - `produce_date`：生产日期
  - `invp_area_no`：仓库区域编号

## 基础数据表

### 6. **pd_part** - 物料主数据表
- **作用**：存储物料的基本信息
- **关键字段**：
  - `part_no`：物料编码
  - `part_name`：物料名称
  - `part_spec`：物料规格
  - `part_unit`：计量单位
  - `is_fifo`：是否先进先出管控

### 7. **ss_user** - 用户表
- **作用**：存储系统用户信息
- **关键字段**：
  - `user_id`：用户ID
  - `user_no`：用户编号
  - `user_name`：用户姓名

### 8. **ss_cdvl** - 代码值表
- **作用**：存储系统状态码和描述信息
- **关键字段**：
  - `cdvl_no`：代码值
  - `cdtp_id`：代码类型ID

## 质量管理相关表

### 9. **szjy_qm_oba_sn** - 品质检验装箱记录表
- **作用**：记录产品的质量检验和装箱信息
- **关键字段**：
  - `oba_sn_id`：记录ID
  - `carton_no`：箱号
  - `sn_no_orig`：原始序列号
  - `sn_no_inspec`：检验序列号
  - `mo_no`：工单号

## 销售退货相关表

### 10. **cr_rtn_h** - 销售退货单头表
- **作用**：存储销售退货单信息（从接口代码推断）
- **关键字段**：
  - `cr_rtn_h_no`：退货单号
  - `sap_bill_no`：SAP单据号

## 功能流程总结

1. **销售出库拣货流程**：通过`wm_sn`表验证序列号状态，更新`cr_dlv_sn_part`记录拣货信息，更新`cr_dlv_b`和`cr_dlv_h`的状态

2. **无序列号销售出库**：直接从`szjy_wm_inventory`扣减库存，生成`cr_dlv_sn_part`记录

3. **MES-SAP接口**：将完成发货的单据数据推送到SAP系统，更新`cr_dlv_h`表的`sap_bill_no`字段

4. **质量检验**：通过`szjy_qm_oba_sn`表查询产品的检验装箱记录

这些表构成了完整的销售出库管理体系，涵盖了从拣货、发货到与SAP系统集成的全流程。
        