
CREATE PROC [dbo].[af_ax_qc_after]
AS
BEGIN
Set NoCount On
	declare @error  int				-- Result (0 for no error)
	declare @error_message nvarchar (2000) 		-- Error string to be displayed
	begin transaction tran_ins
	begin try
		-- 2022.09.22 yy 从ax_qc中删除失败且在临时表ax_qc_temp中存在的收货单
		DELETE ax_qc 
		  FROM ax_qc a
		  INNER JOIN ax_qc_temp b ON b.delivery_order_no=a.delivery_order_no
		WHERE a.return_code<>'0' or a.return_code is null
		
		insert into ax_qc(
			_id ,po_no ,erp_pod_pk ,return_no ,return_code ,return_msg ,upd_time
			,create_yh_xm,dh_sl,receiving_qty ,receiving_date ,lot_no ,delivery_order_no 
			,erp_user_no ,erp_deptno ,supplier_no ,supplier_name ,part_no 
			,part_name ,part_spec ,part_unit_no ,part_unit_name ,ng_qty 
			,wm_tms ,erp_org_no ,pod_remark ,scm_update_time ,erp_extension_type
			,remark_01 ,remark_02 ,remark_03 ,remark_04 ,remark_05 
			,remark_06 ,remark_07 ,remark_08 ,remark_09 ,remark_10
			)
		select _id ,po_no ,erp_pod_pk ,return_no ,return_code ,return_msg ,upd_time
			,create_yh_xm,dh_sl,receiving_qty ,receiving_date ,lot_no ,delivery_order_no 
			,erp_user_no ,erp_deptno ,supplier_no ,supplier_name ,part_no 
			,part_name ,part_spec ,part_unit_no ,part_unit_name ,ng_qty 
			,wm_tms ,erp_org_no ,pod_remark ,scm_update_time ,erp_extension_type
			,remark_01 ,remark_02 ,remark_03 ,remark_04 ,remark_05 
			,remark_06 ,remark_07 ,remark_08 ,remark_09 ,remark_10
		from ax_qc_temp a
		where not exists(select 1 from ax_qc where delivery_order_no=a.delivery_order_no)
		commit transaction tran_ins
	end try
	begin catch
		rollback transaction tran_ins
	end catch

	begin transaction tran_upd
	begin try
		update a
  		   set a.po_no=b.po_no ,a.erp_pod_pk=b.erp_pod_pk ,a.return_no=b.return_no ,a.return_code=b.return_code 
  			  ,a.return_msg=b.return_msg ,a.upd_time=b.upd_time ,a.create_yh_xm=b.create_yh_xm ,a.dh_sl=b.dh_sl 
  			  ,a.receiving_qty=b.receiving_qty ,a.receiving_date=b.receiving_date ,a.lot_no=b.lot_no ,a.delivery_order_no=b.delivery_order_no 
  			  ,a.erp_user_no=b.erp_user_no ,a.erp_deptno=b.erp_deptno ,a.supplier_no=b.supplier_no ,a.supplier_name=b.supplier_name 
  			  ,a.part_no=b.part_no ,a.part_name=b.part_name ,a.part_spec=b.part_spec ,a.part_unit_no=b.part_unit_no 
  			  ,a.part_unit_name=b.part_unit_name ,a.ng_qty=b.ng_qty ,a.wm_tms=b.wm_tms ,a.erp_org_no=b.erp_org_no 
  			  ,a.pod_remark=b.pod_remark ,a.scm_update_time=b.scm_update_time,a.erp_extension_type=b.erp_extension_type
			,a.remark_01=b.remark_01 ,a.remark_02=b.remark_02 ,a.remark_03=b.remark_03 ,a.remark_04=b.remark_04 ,a.remark_05=b.remark_05
			,a.remark_06=b.remark_06 ,a.remark_07=b.remark_07 ,a.remark_08=b.remark_08 ,a.remark_09=b.remark_09 ,a.remark_10=b.remark_10
		from ax_qc a, ax_qc_temp b
		where a._id=b._id and a.delivery_order_no=b.delivery_order_no and (a.return_code<>'0' or a.return_code is null)
		commit transaction tran_upd
	end try
	begin catch
		rollback transaction tran_upd
	end catch

	declare @_id varchar(100) ,@po_no varchar(100) ,@erp_pod_pk varchar(100) ,@return_code varchar(100) ,@return_msg nvarchar(4000) 
		,@upd_time datetime ,@receiving_qty decimal(18,6) ,@receiving_date varchar(100) ,@lot_no varchar(100) ,@delivery_order_no varchar(100) 
		,@erp_user_no varchar(100) ,@erp_deptno varchar(100) ,@supplier_no varchar(100) ,@supplier_name nvarchar(200) 
		,@part_no varchar(100) ,@part_name nvarchar(200) ,@part_spec nvarchar(3000) ,@part_unit_no varchar(100) ,@part_unit_name nvarchar(200) 
		,@ng_qty decimal(18,6) ,@erp_org_no varchar(100), @part_name_full nvarchar(3000)
		,@po_qty decimal(18,6)
		,@sr_dlv_id int
		,@str_rec_date varchar(100),@rec_hour varchar(10),@rec_min varchar(10),@str_hm varchar(10)
		,@create_time int,@i_hour int,@i_min int,@week_diff int,@cur_week int
		,@create_date datetime,@due_date datetime
		,@line_index int,@iLen int,@iPos int,@line_id int,@vis_order int,@s_line_id varchar(10)
		,@user_sign varchar(50), @scm_man nvarchar(50), @s_qctemp nvarchar(50),@rec_man nvarchar(50)
		,@u_type varchar(100), @u_type_name nvarchar(50)
		,@u_batch_no varchar(50),@msg nvarchar(3000)

		,@err_msg nvarchar(3000), @err_severity int, @err_state int, @err_no int, @err_line int
	SELECT @error = 0, @error_message = N'Ok'
	select @week_diff = 150 - DATEPART(week, '2022-06-21')
		 , @cur_week = @week_diff + DATEPART(week, GetDate())
		 , @user_sign = '119',@scm_man = 'MES',@s_qctemp = 'QCTEMP',@s_qctemp = 'QCTEMP'
	--print @cur_week
    --begin transaction tranbill
    --    begin try
			declare cur_h cursor
				for (select distinct a.delivery_order_no,upper(a.supplier_no),SUBSTRING(b.supplier_name,1,20),a.receiving_date
					,SUBSTRING(a.receiving_date,1,10) + ' 00:00:00' as str_rec_date
					,SUBSTRING(a.receiving_date,12,2) as rec_hour
					,SUBSTRING(a.receiving_date,15,2) as rec_min
					,CONVERT(varchar(100), dateadd(day,0,SUBSTRING(a.receiving_date,1,10) + ' 00:00:00'), 120) as create_date
					,CONVERT(int,SUBSTRING(a.receiving_date,12,2)+SUBSTRING(a.receiving_date,15,2)) as create_time
					,CONVERT(varchar(100), dateadd(day,30,SUBSTRING(a.receiving_date,1,10) + ' 00:00:00'), 120) as due_date
					,a.create_yh_xm as rec_man
					from ax_qc a
					left join SapToMes_Ocrd_GYS_vi b on b.supplier_no=upper(a.supplier_no)
					where 1=1
					  --and not exists(select 1 from iomaster_data_log_62be8aa950e3c2735c6b5242 where cast(pk_id as varchar(100))=a._id and cast(return_code as varchar(100))='0')
					  and (a.return_code<>'0' or a.return_code is null)
					  and a.receiving_date>='2022-07-12 00:00:00'
					)
			open cur_h
			fetch next from cur_h into @delivery_order_no,@supplier_no,@supplier_name,@receiving_date
				,@str_rec_date,@rec_hour,@rec_min,@create_date,@create_time,@due_date
				,@rec_man
			while @@FETCH_STATUS = 0
			begin
				begin transaction tranbody
				begin try
					 --从视图中取SAP送检单号最大值+1
					select @sr_dlv_id=max(convert(int,sr_dlv_id))+1 from SapToMes_QcTemp_vi

--					print Concat(@receiving_date,' 收货时间:',@str_rec_date,' 时:',@rec_hour,' 分:',@rec_min,' 收货日期:',@create_date,' 收货时间:', @create_time)
					insert into [@QCTEMP](
						 [DocEntry] ,[DocNum] ,[Period] ,[Series] ,[Canceled],[U_MesSo]
						,[Object] ,[UserSign] ,[Status] ,[CreateDate] ,[CreateTime] ,[Creator] 
						,[U_CardCode] ,[U_CardName] ,[U_status] ,[U_DueDate] ,[U_DueDateT] ,[U_DocDate] 
						,[U_Sman] ,[U_Crman] ,[U_Cman] ,[U_MES])
					values(@sr_dlv_id,@sr_dlv_id,@cur_week, 121, 'N',@delivery_order_no
						,@s_qctemp, @user_sign, 'O', @create_date, @create_time, @scm_man
						,@supplier_no,@supplier_name, 'Y', @due_date,@due_date, @create_date
						,@rec_man, @scm_man, @scm_man, 'Y'
						)
					
					declare @cur_b cursor
					set @cur_b = cursor	for (select po_no,upper(erp_pod_pk),upper(part_no),part_name,part_spec,part_unit_name
						,substring(concat(part_name,part_spec),1,100),receiving_qty ,ng_qty,dh_sl ,erp_extension_type
							from ax_qc where delivery_order_no=@delivery_order_no)
					set @line_index = 0
					open @cur_b
					fetch next from @cur_b into @po_no,@erp_pod_pk,@part_no,@part_name,@part_spec,@part_unit_name
						,@part_name_full,@receiving_qty,@ng_qty,@po_qty, @u_type
					while @@FETCH_STATUS = 0
					begin
						--print Concat(@sr_dlv_id,' ',@line_id)
						set @line_index = @line_index + 1
						if @line_index<2
						begin
							set @vis_order = 1
						end
						else
						begin
							set @vis_order = @line_index - 1
						end
						set @u_type_name='订单'
						if upper(@u_type)='T'
						begin
							set @u_type_name = '退货'
						end
						
--						print(concat(@erp_pod_pk,' ',@line_index,' ',@vis_order,' ',@u_type,' ',@u_type_name))	
						
						select @iLen=len(@erp_pod_pk), @iPos = charindex('T_',@erp_pod_pk)
						if @iPos = 1
						BEGIN 
							set @erp_pod_pk = substring(@erp_pod_pk, 3, @iLen - 2)
							set @po_no = substring(@po_no, 3, @iLen - 2)
							
						END	
--						print(concat('T_xxx ',@erp_pod_pk,' ',@line_index,' ',@vis_order,' ',@u_type,' ',@u_type_name))
						
						select @iLen=len(@erp_pod_pk),@iPos=charindex('_',@erp_pod_pk)
						select @s_line_id = substring(@erp_pod_pk, @iPos + 1, @iLen - @iPos)
						select @line_id = convert(int, @s_line_id)
						-- 批次号 S20220614694231 MES产生=M +年月日+送检单号+送检明细行号
						--select @u_batch_no=concat('M',convert(varchar(100),GetDate(),112),@sr_dlv_id,@line_index)
						select @u_batch_no='MES888888'
						--print Concat(@sr_dlv_id,' ',@erp_pod_pk,' ',@line_id,' ',@vis_order)
						insert into [@QCTEMP1]( 
							 [DocEntry] ,[LineId] ,[VisOrder] ,[Object] ,[U_Type]
							,[U_ItemCode] ,[U_ItemName] ,[U_ShipDate] ,[U_Unit] 
							,[U_POQTY] ,[U_Quantity] ,[U_Openqty] 
							,[U_Lstatus] ,[U_qcstatus] ,[U_KYCD] ,[U_CJqty] ,[U_bentry] ,[U_bline] 
							,[U_inQTY] ,[U_OKQTY] ,[U_TCQTY] ,[U_NGQTY] ,[U_YTQTY] 
							,[U_2DZ] ,[U_1DYPH])
						values(@sr_dlv_id, @line_index, @vis_order, @s_qctemp, @u_type_name
							,@part_no, @part_name_full, @create_date, @part_unit_name
							, @po_qty, @receiving_qty, @receiving_qty
							, 'O' ,1 ,1 ,0,@po_no , @line_id
							, 0,0,0,0,0
							, 'N', @u_batch_no)
						--set @line_index = @line_index + 1
						fetch next from @cur_b into @po_no,@erp_pod_pk,@part_no,@part_name,@part_spec,@part_unit_name,@part_name_full,@receiving_qty,@ng_qty, @po_qty, @u_type
					end
					close @cur_b;
					deallocate @cur_b;

					begin
						EXEC [dbo].[Ax_Sp_TransactionNotification] 'QCTEMP' ,'A',Null,Null,@sr_dlv_id,@error output,@error_message OUTPUT						
						if @error = 1
						begin
							--update a set a.return_code='1',a.return_msg=@error_message,a.is_succeed='1'
							--  from [iomaster_data_log_62be8aa950e3c2735c6b5242] a,
							--	[ax_qctemp] b
							-- where a.pk_id=b._id 
							--   and b.delivery_order_no=@delivery_order_no
							update [ax_qc] set [return_code]='1',[return_msg]=@error_message,[upd_time]=GetDate() where delivery_order_no=@delivery_order_no AND ([return_code]<>'0' or [return_code] is null)
							delete from [@qctemp1] where DocEntry=@sr_dlv_id
							delete from [@qctemp] where DocEntry=@sr_dlv_id
						end
						else
						begin
							update [ax_qc] set [return_no]=@sr_dlv_id,[return_code]='0',[return_msg]='同步成功',[upd_time]=GetDate() where delivery_order_no=@delivery_order_no AND ([return_code]<>'0' or [return_code] is null)
							--update a set a.return_code='0',a.return_msg='同步成功',a.is_succeed='0' 
							--from [iomaster_data_log_62be8aa950e3c2735c6b5242] a,
							--	[ax_qctemp] b
							-- where a.pk_id=b._id 
							--   and b.delivery_order_no=@delivery_order_no
						end
					end

					commit transaction tranbody
					begin transaction tran_seq
						exec [dbo].[UpOnnm] 'QCTEMP'
					commit transaction tran_seq
				end try
				begin catch					
					select @err_msg=ERROR_MESSAGE(), @err_severity=ERROR_SEVERITY(), @err_state=ERROR_STATE(), @err_no=error_number(), @err_line=error_line()
					if @@trancount>0
					begin
						rollback transaction tranbody
					end
					----RAISEERROR (@err_msg, @err_severity, @err_state,N'abcd')
					--RAISEERROR (@err_msg, 16,1)
					begin transaction tranlog
						select @msg=concat('状态: ',@err_state,' 行号: ',@err_line,' 编号: ',@err_no,' 信息: ',@err_msg)
						print(concat(@sr_dlv_id,' ',@delivery_order_no,' ',@supplier_no,' ',@supplier_name,' ',@rec_man,' ',@msg))
						--if @err_no=2627
						--begin
						--end
						update [ax_qc] set [return_code]='1',[return_msg]=@err_msg,[remark_01]=@msg,[upd_time]=GetDate() where delivery_order_no=@delivery_order_no AND ([return_code]<>'0' or [return_code] is null)
						--update a set a.return_code='1',a.return_msg=@err_msg,a.is_succeed='1',remark01=@msg
						--	  from [iomaster_data_log_62be8aa950e3c2735c6b5242] a,
						--		[ax_qctemp] b
						--	 where a.pk_id=b._id 
						--	   and b.delivery_order_no=@delivery_order_no
					commit transaction tranlog
				end catch
				fetch next from cur_h into @delivery_order_no,@supplier_no,@supplier_name,@receiving_date
					,@str_rec_date,@rec_hour,@rec_min,@create_date,@create_time,@due_date
					,@rec_man
			end
			close cur_h;
			deallocate cur_h;
        --   commit transaction tranbill
        --end try
        --begin catch
        --    rollback transaction tranbill
 --end catch
Set NoCount Off
END

--exec [dbo].[af_ax_qc_after]
--go